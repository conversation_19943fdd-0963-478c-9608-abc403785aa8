# 代码风格与规范

## Java代码规范

### 通用返回结构
项目使用统一的API返回结构 `ApiResponse<T>`：
```java
public class ApiResponse<T> {
    private int code;           // 响应码
    private String message;     // 响应消息  
    private T data;            // 响应数据
    private long timestamp;    // 时间戳
    private String requestId;  // 请求ID
}
```

### 错误码规范
使用枚举定义错误码：
```java
public enum ErrorCode {
    SUCCESS(200, "成功"),
    BAD_REQUEST(400, "请求参数错误"),
    UNAUTHORIZED(401, "未授权"),
    FORBIDDEN(403, "禁止访问"),
    NOT_FOUND(404, "资源不存在"),
    INTERNAL_ERROR(500, "服务器内部错误");
}
```

### 命名规范
- 类名：使用PascalCase (如: ApiResponse, ErrorCode)
- 方法名：使用camelCase (如: success, fail, isSuccess)
- 常量：使用UPPER_SNAKE_CASE (如: SUCCESS, BAD_REQUEST)
- 包名：使用小写字母，用点分隔

### 注释规范
- 类级别使用JavaDoc注释，包含类的用途和泛型参数说明
- 方法使用简洁的注释说明功能
- 重要字段添加行内注释

### 代码结构规范
- 统一异常处理机制
- 统一响应格式
- 日志框架配置 (Log4j2异步日志)
- API接口文档规范
- 单元测试覆盖率要求

## 前端代码规范
- 基于Vue3框架开发
- 使用soybean-admin作为UI框架
- 代码结构清晰，遵循最佳实践
- 统一的路由配置和状态管理

## 项目组织规范
- 前端代码放置于frontend/bms-page目录
- 后端代码放置于backend目录下对应模块
- 文档放置于doc目录
- 遵循模块化开发原则