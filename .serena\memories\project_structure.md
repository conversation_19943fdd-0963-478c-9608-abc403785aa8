# 项目结构

## 总体目录结构
```
backend_management_system_v3/
├── backend/           # 后端代码目录
├── frontend/          # 前端代码目录
├── doc/              # 文档目录
├── .gitignore        # Git忽略文件
├── README.md         # 项目说明文档
└── 提示词.txt        # 项目提示词文件
```

## 后端模块结构 (backend/)
1. **bms-api** - 对外提供服务，校验输入参数，与bms-biz模块交互
2. **bms-biz** - 业务逻辑处理，报文转换，与bms-router模块交互  
3. **bms-router** - 负责请求其他系统
4. **bms-manage** - 后台管理页面接口，需要登录，与bms-biz、bms-batch模块交互
5. **bms-batch** - 批量处理模块
6. **bms-common** - 后端公共模块，包含共享的类、接口、方法等

## 前端模块结构 (frontend/)
1. **bms-page** - 后台管理模块页面，请求bms-manage模块提供的接口

## 文档目录 (doc/)
- `ApiResponse.java` - 通用返回结构示例
- `ErrorCode.java` - 错误码枚举示例
- `架构网络拓扑图（单中心）V4.html` - 系统架构图

## 模块交互关系
- bms-page (前端) → bms-manage (后端管理接口)
- bms-api → bms-biz → bms-router
- bms-manage → bms-biz, bms-batch
- 所有模块都依赖 bms-common

## 当前状态
项目目前处于初始化阶段，backend和frontend目录为空，需要根据架构设计创建具体的模块结构。