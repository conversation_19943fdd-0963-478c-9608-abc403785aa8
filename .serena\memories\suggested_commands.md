# 建议的开发命令

## Windows系统命令
由于项目运行在Windows系统上，以下是常用的系统命令：

### 文件系统操作
- `dir` - 列出目录内容 (相当于Unix的ls)
- `cd <directory>` - 切换目录
- `mkdir <directory>` - 创建目录
- `rmdir <directory>` - 删除空目录
- `del <file>` - 删除文件
- `copy <source> <destination>` - 复制文件
- `move <source> <destination>` - 移动文件

### 文本搜索
- `findstr <pattern> <file>` - 在文件中搜索文本 (相当于Unix的grep)
- `find "<text>" <file>` - 查找包含指定文本的行

## Java/Maven命令

### 后端开发命令
- `mvn clean compile` - 清理并编译项目
- `mvn clean package` - 打包项目
- `mvn spring-boot:run` - 运行Spring Boot应用
- `mvn test` - 运行单元测试
- `mvn clean install` - 清理、编译、测试并安装到本地仓库

### 各模块启动命令
- `cd backend/bms-manage && mvn spring-boot:run` - 启动管理模块
- `cd backend/bms-api && mvn spring-boot:run` - 启动API模块
- `cd backend/bms-biz && mvn spring-boot:run` - 启动业务模块
- `cd backend/bms-router && mvn spring-boot:run` - 启动路由模块
- `cd backend/bms-batch && mvn spring-boot:run` - 启动批处理模块

## Node.js/前端命令

### 前端开发命令
- `npm install` - 安装依赖
- `npm run dev` - 启动开发服务器
- `npm run build` - 构建生产版本
- `npm run test` - 运行前端测试
- `npm run lint` - 代码检查

### 前端模块启动
- `cd frontend/bms-page && npm run dev` - 启动前端管理页面

## Git命令
- `git status` - 查看仓库状态
- `git add .` - 添加所有更改到暂存区
- `git commit -m "message"` - 提交更改
- `git push` - 推送到远程仓库
- `git pull` - 拉取远程更改
- `git branch` - 查看分支
- `git checkout <branch>` - 切换分支

## Docker命令 (如果使用容器化部署)
- `docker build -t <image-name> .` - 构建镜像
- `docker run -p <port>:<port> <image-name>` - 运行容器
- `docker ps` - 查看运行中的容器
- `docker logs <container-id>` - 查看容器日志

## 数据库命令
- `mysql -u <username> -p` - 连接MySQL数据库
- `redis-cli` - 连接Redis

## 注意事项
- 项目目前处于初始化阶段，backend和frontend目录为空
- 需要先创建各模块的基础结构才能执行相应的启动命令
- 确保已安装所需的开发环境 (JDK8, Maven, Node.js等)