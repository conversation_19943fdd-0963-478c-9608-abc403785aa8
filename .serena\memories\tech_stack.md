# 技术栈

## 前端技术栈
- **框架**: Vue3
- **UI框架**: Soybean-Admin
- **基础技术**: HTML + CSS
- **运行环境**: Node.js 16+

## 后端技术栈
- **核心框架**: SpringBoot + JDK8
- **应用服务器**: Tomcat
- **数据访问**: MyBatis
- **数据库**: 
  - MySQL 5.7 (分库分表)
  - TDSql数据库集群
- **缓存**: Redis集群 (缓存、分布式锁、防重复提交)
- **服务治理**:
  - Nacos (服务注册发现、配置管理)
  - SpringCloudGateway (API网关)
  - Feign Client (服务调用)
- **消息队列**: 
  - RocketMQ
  - Kafka
  - Spring Cloud Stream
- **数据同步**: 
  - Canal (数据同步)
  - Databus (ETL工具)
- **监控与日志**:
  - SkyWalking (分布式链路跟踪)
  - Prometheus (性能监控)
  - Log4j2 (异步日志)
- **网关层**:
  - Nginx/OpenResty (负载均衡、静态资源)

## 开发环境要求
- JDK 8
- Maven 3.6+
- Node.js 16+
- MySQL 5.7
- Redis 6+

## 部署技术
- Docker (容器化)
- Kubernetes (集群管理)
- 持续集成/持续部署