/* 全局样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Microsoft YaHei', Arial, sans-serif;
    background-color: #f5f5f5;
    color: #333;
}

/* 顶部导航栏 */
.header {
    height: 60px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 20px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
}

.header-left .logo {
    display: flex;
    align-items: center;
    font-size: 18px;
    font-weight: bold;
}

.header-left .logo i {
    margin-right: 10px;
    font-size: 24px;
}

.header-right {
    display: flex;
    align-items: center;
    gap: 20px;
}

.user-info {
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
    padding: 8px 12px;
    border-radius: 4px;
    transition: background-color 0.3s;
}

.user-info:hover {
    background-color: rgba(255,255,255,0.1);
}

.logout {
    cursor: pointer;
    padding: 8px;
    border-radius: 4px;
    transition: background-color 0.3s;
}

.logout:hover {
    background-color: rgba(255,255,255,0.1);
}

/* 主容器 */
.container {
    display: flex;
    margin-top: 60px;
    min-height: calc(100vh - 60px);
}

/* 侧边栏 */
.sidebar {
    width: 250px;
    background-color: #2c3e50;
    color: white;
    overflow-y: auto;
    position: fixed;
    left: 0;
    top: 60px;
    bottom: 0;
}

.nav-menu {
    padding: 20px 0;
}

.menu-item {
    display: flex;
    align-items: center;
    padding: 15px 20px;
    cursor: pointer;
    transition: all 0.3s;
    position: relative;
}

.menu-item:hover {
    background-color: #34495e;
}

.menu-item.active {
    background-color: #3498db;
    border-right: 3px solid #2980b9;
}

.menu-item i {
    width: 20px;
    margin-right: 12px;
    text-align: center;
}

.menu-item .arrow {
    margin-left: auto;
    margin-right: 0;
    transition: transform 0.3s;
}

.menu-item.expanded .arrow {
    transform: rotate(90deg);
}

.submenu {
    background-color: #1a252f;
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease-out;
}

.submenu.show {
    max-height: 200px;
}

.submenu-item {
    padding: 12px 20px 12px 52px;
    cursor: pointer;
    transition: background-color 0.3s;
    font-size: 14px;
}

.submenu-item:hover {
    background-color: #2c3e50;
}

.submenu-item.active {
    background-color: #3498db;
}

/* 主内容区域 */
.main-content {
    flex: 1;
    margin-left: 250px;
    padding: 20px;
    background-color: #f8f9fa;
}

/* 面包屑导航 */
.breadcrumb {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    padding: 10px 0;
    color: #666;
}

.breadcrumb-item {
    font-size: 14px;
}

.breadcrumb-item.current {
    color: #3498db;
    font-weight: 500;
}

.breadcrumb i {
    margin: 0 8px;
    font-size: 12px;
}

/* 内容区域 */
.content-area {
    background-color: white;
    border-radius: 8px;
    padding: 24px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    min-height: calc(100vh - 140px);
}

.page-content {
    display: none;
}

.page-content.active {
    display: block;
}

.page-content h2 {
    margin-bottom: 24px;
    color: #2c3e50;
    font-size: 24px;
    font-weight: 600;
}

/* 仪表板卡片 */
.dashboard-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.card {
    background: white;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    display: flex;
    align-items: center;
    transition: transform 0.3s, box-shadow 0.3s;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0,0,0,0.15);
}

.card-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 16px;
    font-size: 24px;
    color: white;
}

.card:nth-child(1) .card-icon {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.card:nth-child(2) .card-icon {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.card:nth-child(3) .card-icon {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.card:nth-child(4) .card-icon {
    background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.card-content h3 {
    font-size: 16px;
    color: #666;
    margin-bottom: 8px;
}

.card-content .number {
    font-size: 28px;
    font-weight: bold;
    color: #2c3e50;
    margin-bottom: 4px;
}

.card-content .status {
    font-size: 20px;
    font-weight: bold;
    margin-bottom: 4px;
}

.status.online {
    color: #27ae60;
}

.number.warning {
    color: #e74c3c;
}

.card-content small {
    color: #999;
    font-size: 12px;
}

/* 系统架构图 */
.architecture-section {
    margin-top: 40px;
}

.architecture-section h3 {
    margin-bottom: 20px;
    color: #2c3e50;
    font-size: 20px;
}

.architecture-diagram {
    display: flex;
    flex-direction: column;
    gap: 20px;
    padding: 20px;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    border-radius: 12px;
}

.layer {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 20px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.layer h4 {
    margin-bottom: 15px;
    color: #2c3e50;
    font-size: 16px;
    font-weight: 600;
}

.layer .module {
    display: inline-block;
    margin: 5px;
    padding: 12px 16px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 6px;
    font-size: 14px;
    text-align: center;
    min-width: 120px;
    transition: transform 0.3s;
}

.layer .module:hover {
    transform: scale(1.05);
}

.layer .module small {
    display: block;
    font-size: 11px;
    opacity: 0.8;
    margin-top: 2px;
}

/* 不同层级的模块颜色 */
.frontend-layer .module {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.gateway-layer .module {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.service-layer .module {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.data-layer .module {
    background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.middleware-layer .module {
    background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .sidebar {
        width: 200px;
    }
    
    .main-content {
        margin-left: 200px;
    }
    
    .dashboard-cards {
        grid-template-columns: 1fr;
    }
    
    .architecture-diagram {
        padding: 15px;
    }
    
    .layer .module {
        min-width: 100px;
        font-size: 12px;
    }
}

/* RBAC相关样式 */
.rbac-overview {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.rbac-card {
    background: white;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    display: flex;
    align-items: center;
    transition: transform 0.3s;
}

.rbac-card:hover {
    transform: translateY(-2px);
}

.rbac-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
    font-size: 20px;
    color: white;
}

.rbac-card:nth-child(1) .rbac-icon {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.rbac-card:nth-child(2) .rbac-icon {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.rbac-card:nth-child(3) .rbac-icon {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.rbac-card:nth-child(4) .rbac-icon {
    background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.rbac-info h4 {
    margin-bottom: 5px;
    color: #2c3e50;
    font-size: 16px;
}

.rbac-info p {
    color: #666;
    font-size: 12px;
    margin-bottom: 5px;
}

.rbac-info .count {
    color: #3498db;
    font-weight: bold;
    font-size: 14px;
}

/* RBAC关系图 */
.rbac-relationship {
    margin: 30px 0;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;
}

.relationship-diagram {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 20px;
    flex-wrap: wrap;
    margin-top: 20px;
}

.rbac-entity {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 15px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    min-width: 80px;
}

.rbac-entity i {
    font-size: 24px;
    margin-bottom: 8px;
}

.user-entity i { color: #3498db; }
.role-entity i { color: #e74c3c; }
.permission-entity i { color: #f39c12; }
.resource-entity i { color: #27ae60; }

.relationship-arrow {
    display: flex;
    flex-direction: column;
    align-items: center;
    color: #666;
}

.relationship-arrow span {
    font-size: 12px;
    margin-bottom: 5px;
}

.relationship-arrow i {
    font-size: 18px;
    color: #3498db;
}

/* 权限树样式增强 */
.permission-tree-section {
    margin-top: 30px;
}

.tree-leaf {
    margin-left: 20px;
    padding: 10px;
    border-left: 2px solid #e9ecef;
    margin-bottom: 10px;
}

.permission-actions {
    margin-top: 8px;
    display: flex;
    flex-wrap: wrap;
    gap: 5px;
}

.action-tag {
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 11px;
    font-weight: 500;
}

.action-tag.create {
    background-color: #d4edda;
    color: #155724;
}

.action-tag.read {
    background-color: #cce5ff;
    color: #004085;
}

.action-tag.update {
    background-color: #fff3cd;
    color: #856404;
}

.action-tag.delete {
    background-color: #f8d7da;
    color: #721c24;
}

.action-tag.execute {
    background-color: #e2e3e5;
    color: #383d41;
}

.resource-code {
    font-size: 12px;
    color: #666;
    background: #f8f9fa;
    padding: 2px 6px;
    border-radius: 3px;
    margin-left: 10px;
}

@media (max-width: 480px) {
    .sidebar {
        width: 100%;
        position: relative;
        height: auto;
    }

    .main-content {
        margin-left: 0;
    }

    .container {
        flex-direction: column;
    }

    .rbac-overview {
        grid-template-columns: 1fr;
    }

    .relationship-diagram {
        flex-direction: column;
        gap: 10px;
    }

    .relationship-arrow {
        transform: rotate(90deg);
    }
}