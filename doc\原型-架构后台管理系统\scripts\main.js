// 主要的JavaScript功能
document.addEventListener('DOMContentLoaded', function() {
    // 初始化页面
    initializeApp();
    
    // 绑定事件监听器
    bindEventListeners();
    
    // 显示默认页面
    showPage('dashboard');
});

// 初始化应用
function initializeApp() {
    console.log('后台管理系统原型已加载');
    
    // 设置当前时间
    updateTimestamp();
    
    // 每分钟更新一次时间戳
    setInterval(updateTimestamp, 60000);
}

// 绑定事件监听器
function bindEventListeners() {
    // 菜单项点击事件
    const menuItems = document.querySelectorAll('.menu-item');
    menuItems.forEach(item => {
        item.addEventListener('click', handleMenuClick);
    });
    
    // 子菜单项点击事件
    const submenuItems = document.querySelectorAll('.submenu-item');
    submenuItems.forEach(item => {
        item.addEventListener('click', handleSubmenuClick);
    });
    
    // 用户信息点击事件
    const userInfo = document.querySelector('.user-info');
    if (userInfo) {
        userInfo.addEventListener('click', handleUserInfoClick);
    }
    
    // 登出按钮点击事件
    const logout = document.querySelector('.logout');
    if (logout) {
        logout.addEventListener('click', handleLogout);
    }
}

// 处理菜单点击
function handleMenuClick(event) {
    const menuItem = event.currentTarget;
    const module = menuItem.getAttribute('data-module');
    const arrow = menuItem.querySelector('.arrow');
    const submenu = menuItem.nextElementSibling;
    
    // 移除其他菜单的active状态
    document.querySelectorAll('.menu-item').forEach(item => {
        if (item !== menuItem) {
            item.classList.remove('active', 'expanded');
            const otherSubmenu = item.nextElementSibling;
            if (otherSubmenu && otherSubmenu.classList.contains('submenu')) {
                otherSubmenu.classList.remove('show');
            }
        }
    });
    
    // 如果有子菜单，切换展开状态
    if (submenu && submenu.classList.contains('submenu')) {
        menuItem.classList.toggle('expanded');
        submenu.classList.toggle('show');
        
        // 如果展开子菜单，不设置当前菜单为active
        if (!submenu.classList.contains('show')) {
            menuItem.classList.add('active');
            showPage(module);
            updateBreadcrumb([getModuleName(module)]);
        }
    } else {
        // 没有子菜单，直接激活
        menuItem.classList.add('active');
        showPage(module);
        updateBreadcrumb([getModuleName(module)]);
    }
}

// 处理子菜单点击
function handleSubmenuClick(event) {
    event.stopPropagation();
    const submenuItem = event.currentTarget;
    const page = submenuItem.getAttribute('data-page');
    const parentMenu = submenuItem.closest('.submenu').previousElementSibling;
    const module = parentMenu.getAttribute('data-module');
    
    // 移除其他子菜单的active状态
    document.querySelectorAll('.submenu-item').forEach(item => {
        item.classList.remove('active');
    });
    
    // 激活当前子菜单项
    submenuItem.classList.add('active');
    
    // 显示对应页面
    showPage(page);
    
    // 更新面包屑
    updateBreadcrumb([getModuleName(module), getPageName(page)]);
}

// 显示页面内容
function showPage(pageId) {
    // 隐藏所有页面内容
    const allPages = document.querySelectorAll('.page-content');
    allPages.forEach(page => {
        page.classList.remove('active');
    });
    
    // 显示目标页面，如果不存在则创建
    let targetPage = document.getElementById(pageId);
    if (!targetPage) {
        targetPage = createPageContent(pageId);
    }
    
    if (targetPage) {
        targetPage.classList.add('active');
    }
}

// 创建页面内容
function createPageContent(pageId) {
    const contentArea = document.getElementById('content-area');
    const pageContent = document.createElement('div');
    pageContent.className = 'page-content';
    pageContent.id = pageId;
    
    // 根据页面ID生成不同的内容
    const content = generatePageContent(pageId);
    pageContent.innerHTML = content;
    
    contentArea.appendChild(pageContent);
    return pageContent;
}

// 生成页面内容
function generatePageContent(pageId) {
    const pageConfigs = {
        'dashboard': {
            title: '仪表板',
            content: `
                <!-- 系统概览统计 -->
                <div class="dashboard-overview">
                    <div class="overview-cards">
                        <div class="overview-card">
                            <div class="card-icon users">
                                <i class="fas fa-users"></i>
                            </div>
                            <div class="card-content">
                                <h3>用户总数</h3>
                                <span class="card-number">1,234</span>
                                <small class="card-trend up">+12% 本月</small>
                            </div>
                        </div>
                        <div class="overview-card">
                            <div class="card-icon services">
                                <i class="fas fa-server"></i>
                            </div>
                            <div class="card-content">
                                <h3>系统服务</h3>
                                <span class="card-number">8</span>
                                <small class="card-trend stable">运行正常</small>
                            </div>
                        </div>
                        <div class="overview-card">
                            <div class="card-icon api">
                                <i class="fas fa-chart-line"></i>
                            </div>
                            <div class="card-content">
                                <h3>API调用</h3>
                                <span class="card-number">45.6K</span>
                                <small class="card-trend up">+8% 今日</small>
                            </div>
                        </div>
                        <div class="overview-card">
                            <div class="card-icon alerts">
                                <i class="fas fa-exclamation-triangle"></i>
                            </div>
                            <div class="card-content">
                                <h3>系统告警</h3>
                                <span class="card-number">3</span>
                                <small class="card-trend warning">需要处理</small>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 快速操作面板 -->
                <div class="quick-actions">
                    <h3>快速操作</h3>
                    <div class="action-grid">
                        <div class="action-item" onclick="switchPage('user-management')">
                            <i class="fas fa-user-plus"></i>
                            <span>新增用户</span>
                        </div>
                        <div class="action-item" onclick="switchPage('api-management')">
                            <i class="fas fa-code"></i>
                            <span>API管理</span>
                        </div>
                        <div class="action-item" onclick="switchPage('system-params')">
                            <i class="fas fa-cog"></i>
                            <span>系统配置</span>
                        </div>
                        <div class="action-item" onclick="switchPage('task-monitor')">
                            <i class="fas fa-tasks"></i>
                            <span>任务监控</span>
                        </div>
                        <div class="action-item" onclick="switchPage('performance')">
                            <i class="fas fa-chart-bar"></i>
                            <span>性能监控</span>
                        </div>
                        <div class="action-item" onclick="switchPage('system-logs')">
                            <i class="fas fa-file-alt"></i>
                            <span>系统日志</span>
                        </div>
                    </div>
                </div>

                <!-- 系统状态监控 -->
                <div class="system-status">
                    <h3>系统状态</h3>
                    <div class="status-grid">
                        <div class="status-item">
                            <div class="status-header">
                                <h4>服务状态</h4>
                                <span class="status-indicator online"></span>
                            </div>
                            <div class="service-list">
                                <div class="service-item">
                                    <span class="service-name">bms-api</span>
                                    <span class="service-status running">运行中</span>
                                    <span class="service-uptime">99.9%</span>
                                </div>
                                <div class="service-item">
                                    <span class="service-name">bms-manage</span>
                                    <span class="service-status running">运行中</span>
                                    <span class="service-uptime">99.8%</span>
                                </div>
                                <div class="service-item">
                                    <span class="service-name">bms-biz</span>
                                    <span class="service-status running">运行中</span>
                                    <span class="service-uptime">99.7%</span>
                                </div>
                            </div>
                        </div>

                        <div class="status-item">
                            <div class="status-header">
                                <h4>资源使用</h4>
                                <span class="status-indicator normal"></span>
                            </div>
                            <div class="resource-metrics">
                                <div class="metric-item">
                                    <span class="metric-label">CPU使用率</span>
                                    <div class="metric-bar">
                                        <div class="metric-fill" style="width: 45%"></div>
                                    </div>
                                    <span class="metric-value">45%</span>
                                </div>
                                <div class="metric-item">
                                    <span class="metric-label">内存使用</span>
                                    <div class="metric-bar">
                                        <div class="metric-fill" style="width: 68%"></div>
                                    </div>
                                    <span class="metric-value">68%</span>
                                </div>
                                <div class="metric-item">
                                    <span class="metric-label">磁盘使用</span>
                                    <div class="metric-bar">
                                        <div class="metric-fill" style="width: 32%"></div>
                                    </div>
                                    <span class="metric-value">32%</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 系统架构图 -->
                <div class="architecture-diagram">
                    <h3>系统架构图</h3>
                    <div class="arch-container">
                        <div class="arch-layer frontend">
                            <h4>前端层</h4>
                            <div class="arch-components">
                                <div class="component">Vue3</div>
                                <div class="component">Soybean-Admin</div>
                                <div class="component">Element Plus</div>
                            </div>
                        </div>
                        <div class="arch-layer gateway">
                            <h4>网关层</h4>
                            <div class="arch-components">
                                <div class="component">Spring Cloud Gateway</div>
                                <div class="component">Nacos</div>
                            </div>
                        </div>
                        <div class="arch-layer services">
                            <h4>服务层</h4>
                            <div class="arch-components">
                                <div class="component">bms-api</div>
                                <div class="component">bms-manage</div>
                                <div class="component">bms-biz</div>
                                <div class="component">bms-batch</div>
                            </div>
                        </div>
                        <div class="arch-layer data">
                            <h4>数据层</h4>
                            <div class="arch-components">
                                <div class="component">MySQL</div>
                                <div class="component">Redis</div>
                                <div class="component">RocketMQ</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 最近活动 -->
                <div class="recent-activities">
                    <h3>最近活动</h3>
                    <div class="activity-list">
                        <div class="activity-item">
                            <div class="activity-icon">
                                <i class="fas fa-user-plus"></i>
                            </div>
                            <div class="activity-content">
                                <span class="activity-text">管理员创建了新用户 "张三"</span>
                                <span class="activity-time">2分钟前</span>
                            </div>
                        </div>
                        <div class="activity-item">
                            <div class="activity-icon">
                                <i class="fas fa-cog"></i>
                            </div>
                            <div class="activity-content">
                                <span class="activity-text">系统参数 "max_connections" 已更新</span>
                                <span class="activity-time">15分钟前</span>
                            </div>
                        </div>
                        <div class="activity-item">
                            <div class="activity-icon">
                                <i class="fas fa-exclamation-triangle"></i>
                            </div>
                            <div class="activity-content">
                                <span class="activity-text">API "/api/users" 响应时间异常</span>
                                <span class="activity-time">1小时前</span>
                            </div>
                        </div>
                    </div>
                </div>
            `
        },
        'users': {
            title: 'RBAC用户管理',
            content: `
                <div class="page-header">
                    <h2>RBAC用户管理</h2>
                    <div class="header-actions">
                        <button class="btn btn-primary">
                            <i class="fas fa-plus"></i> 新增用户
                        </button>
                        <button class="btn btn-secondary">
                            <i class="fas fa-user-tag"></i> 批量分配角色
                        </button>
                        <button class="btn btn-info">
                            <i class="fas fa-download"></i> 导出用户
                        </button>
                    </div>
                </div>

                <!-- 用户统计 -->
                <div class="user-stats">
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="stat-content">
                            <h4>总用户数</h4>
                            <span class="stat-number">156</span>
                            <small>较上月 +12</small>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-user-check"></i>
                        </div>
                        <div class="stat-content">
                            <h4>活跃用户</h4>
                            <span class="stat-number">142</span>
                            <small>在线率 91%</small>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-user-shield"></i>
                        </div>
                        <div class="stat-content">
                            <h4>管理员</h4>
                            <span class="stat-number">3</span>
                            <small>系统管理员</small>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-user-times"></i>
                        </div>
                        <div class="stat-content">
                            <h4>禁用用户</h4>
                            <span class="stat-number">14</span>
                            <small>需要审核</small>
                        </div>
                    </div>
                </div>

                <!-- 搜索和筛选 -->
                <div class="search-filters">
                    <div class="search-box">
                        <i class="fas fa-search"></i>
                        <input type="text" placeholder="搜索用户名、邮箱..." class="search-input">
                    </div>
                    <div class="filter-group">
                        <select class="filter-select">
                            <option>所有角色</option>
                            <option>系统管理员</option>
                            <option>业务操作员</option>
                            <option>只读用户</option>
                        </select>
                        <select class="filter-select">
                            <option>所有状态</option>
                            <option>启用</option>
                            <option>禁用</option>
                            <option>锁定</option>
                        </select>
                    </div>
                </div>

                <!-- 用户表格 -->
                <div class="table-container">
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th><input type="checkbox" class="select-all"></th>
                                <th>用户ID</th>
                                <th>用户信息</th>
                                <th>分配角色</th>
                                <th>权限概览</th>
                                <th>状态</th>
                                <th>最后登录</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><input type="checkbox"></td>
                                <td>1001</td>
                                <td>
                                    <div class="user-info">
                                        <div class="user-avatar">
                                            <i class="fas fa-user-circle"></i>
                                        </div>
                                        <div class="user-details">
                                            <strong>admin</strong>
                                            <small><EMAIL></small>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <div class="user-roles">
                                        <span class="role-badge admin">系统管理员</span>
                                    </div>
                                </td>
                                <td>
                                    <div class="permission-summary">
                                        <span class="permission-count">89个权限</span>
                                        <div class="permission-preview">
                                            <span class="perm-tag">全部权限</span>
                                        </div>
                                    </div>
                                </td>
                                <td><span class="status-badge active">启用</span></td>
                                <td>2024-01-20 09:15:00</td>
                                <td>
                                    <div class="action-buttons">
                                        <button class="btn btn-sm btn-info" title="编辑用户">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="btn btn-sm btn-warning" title="分配角色">
                                            <i class="fas fa-user-tag"></i>
                                        </button>
                                        <button class="btn btn-sm btn-success" title="重置密码">
                                            <i class="fas fa-key"></i>
                                        </button>
                                        <button class="btn btn-sm btn-danger" title="禁用用户">
                                            <i class="fas fa-ban"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td><input type="checkbox"></td>
                                <td>1002</td>
                                <td>
                                    <div class="user-info">
                                        <div class="user-avatar">
                                            <i class="fas fa-user-circle"></i>
                                        </div>
                                        <div class="user-details">
                                            <strong>operator01</strong>
                                            <small><EMAIL></small>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <div class="user-roles">
                                        <span class="role-badge operator">业务操作员</span>
                                    </div>
                                </td>
                                <td>
                                    <div class="permission-summary">
                                        <span class="permission-count">32个权限</span>
                                        <div class="permission-preview">
                                            <span class="perm-tag">业务管理</span>
                                            <span class="perm-tag">数据查看</span>
                                        </div>
                                    </div>
                                </td>
                                <td><span class="status-badge active">启用</span></td>
                                <td>2024-01-20 08:45:00</td>
                                <td>
                                    <div class="action-buttons">
                                        <button class="btn btn-sm btn-info" title="编辑用户">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="btn btn-sm btn-warning" title="分配角色">
                                            <i class="fas fa-user-tag"></i>
                                        </button>
                                        <button class="btn btn-sm btn-success" title="重置密码">
                                            <i class="fas fa-key"></i>
                                        </button>
                                        <button class="btn btn-sm btn-danger" title="禁用用户">
                                            <i class="fas fa-ban"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td><input type="checkbox"></td>
                                <td>1003</td>
                                <td>
                                    <div class="user-info">
                                        <div class="user-avatar">
                                            <i class="fas fa-user-circle"></i>
                                        </div>
                                        <div class="user-details">
                                            <strong>viewer01</strong>
                                            <small><EMAIL></small>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <div class="user-roles">
                                        <span class="role-badge viewer">只读用户</span>
                                    </div>
                                </td>
                                <td>
                                    <div class="permission-summary">
                                        <span class="permission-count">15个权限</span>
                                        <div class="permission-preview">
                                            <span class="perm-tag">数据查看</span>
                                        </div>
                                    </div>
                                </td>
                                <td><span class="status-badge active">启用</span></td>
                                <td>2024-01-19 16:30:00</td>
                                <td>
                                    <div class="action-buttons">
                                        <button class="btn btn-sm btn-info" title="编辑用户">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="btn btn-sm btn-warning" title="分配角色">
                                            <i class="fas fa-user-tag"></i>
                                        </button>
                                        <button class="btn btn-sm btn-success" title="重置密码">
                                            <i class="fas fa-key"></i>
                                        </button>
                                        <button class="btn btn-sm btn-danger" title="禁用用户">
                                            <i class="fas fa-ban"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <!-- 分页 -->
                <div class="pagination">
                    <button class="btn btn-sm">上一页</button>
                    <span class="page-info">第 1 页，共 10 页</span>
                    <button class="btn btn-sm">下一页</button>
                </div>
            `
        },
        'roles': {
            title: 'RBAC角色管理',
            content: `
                <div class="page-header">
                    <h2>RBAC角色管理</h2>
                    <div class="header-actions">
                        <button class="btn btn-primary">
                            <i class="fas fa-plus"></i> 新增角色
                        </button>
                        <button class="btn btn-secondary">
                            <i class="fas fa-users"></i> 批量分配用户
                        </button>
                    </div>
                </div>

                <!-- 角色统计 -->
                <div class="role-stats">
                    <div class="stat-item">
                        <i class="fas fa-user-shield"></i>
                        <div class="stat-info">
                            <span class="stat-number">12</span>
                            <span class="stat-label">总角色数</span>
                        </div>
                    </div>
                    <div class="stat-item">
                        <i class="fas fa-user-check"></i>
                        <div class="stat-info">
                            <span class="stat-number">156</span>
                            <span class="stat-label">已分配用户</span>
                        </div>
                    </div>
                    <div class="stat-item">
                        <i class="fas fa-key"></i>
                        <div class="stat-info">
                            <span class="stat-number">89</span>
                            <span class="stat-label">权限总数</span>
                        </div>
                    </div>
                </div>

                <!-- 角色列表 -->
                <div class="role-cards">
                    <div class="role-card admin-role">
                        <div class="role-header">
                            <h3>系统管理员</h3>
                            <span class="role-level high">高级</span>
                        </div>
                        <p class="role-description">拥有系统所有权限，负责系统维护和用户管理</p>
                        <div class="role-stats-mini">
                            <span><i class="fas fa-users"></i> 3个用户</span>
                            <span><i class="fas fa-key"></i> 89个权限</span>
                        </div>
                        <div class="role-permissions">
                            <div class="permission-group">
                                <span class="group-title">系统管理</span>
                                <span class="permission-tag create">用户管理</span>
                                <span class="permission-tag create">角色管理</span>
                                <span class="permission-tag create">权限管理</span>
                            </div>
                            <div class="permission-group">
                                <span class="group-title">业务管理</span>
                                <span class="permission-tag create">API管理</span>
                                <span class="permission-tag create">业务逻辑</span>
                            </div>
                            <div class="permission-group">
                                <span class="group-title">监控管理</span>
                                <span class="permission-tag read">性能监控</span>
                                <span class="permission-tag read">日志管理</span>
                            </div>
                        </div>
                        <div class="role-actions">
                            <button class="btn btn-sm btn-info">
                                <i class="fas fa-edit"></i> 编辑权限
                            </button>
                            <button class="btn btn-sm btn-success">
                                <i class="fas fa-user-plus"></i> 分配用户
                            </button>
                            <button class="btn btn-sm btn-warning">
                                <i class="fas fa-copy"></i> 复制角色
                            </button>
                        </div>
                    </div>

                    <div class="role-card operator-role">
                        <div class="role-header">
                            <h3>业务操作员</h3>
                            <span class="role-level medium">中级</span>
                        </div>
                        <p class="role-description">负责日常业务操作，具有业务模块的读写权限</p>
                        <div class="role-stats-mini">
                            <span><i class="fas fa-users"></i> 25个用户</span>
                            <span><i class="fas fa-key"></i> 32个权限</span>
                        </div>
                        <div class="role-permissions">
                            <div class="permission-group">
                                <span class="group-title">业务管理</span>
                                <span class="permission-tag update">API管理</span>
                                <span class="permission-tag read">业务逻辑</span>
                                <span class="permission-tag execute">任务执行</span>
                            </div>
                            <div class="permission-group">
                                <span class="group-title">数据查看</span>
                                <span class="permission-tag read">监控数据</span>
                                <span class="permission-tag read">日志查看</span>
                            </div>
                        </div>
                        <div class="role-actions">
                            <button class="btn btn-sm btn-info">
                                <i class="fas fa-edit"></i> 编辑权限
                            </button>
                            <button class="btn btn-sm btn-success">
                                <i class="fas fa-user-plus"></i> 分配用户
                            </button>
                            <button class="btn btn-sm btn-warning">
                                <i class="fas fa-copy"></i> 复制角色
                            </button>
                        </div>
                    </div>

                    <div class="role-card viewer-role">
                        <div class="role-header">
                            <h3>只读用户</h3>
                            <span class="role-level low">基础</span>
                        </div>
                        <p class="role-description">只能查看数据，无修改权限，适合审计和监控人员</p>
                        <div class="role-stats-mini">
                            <span><i class="fas fa-users"></i> 128个用户</span>
                            <span><i class="fas fa-key"></i> 15个权限</span>
                        </div>
                        <div class="role-permissions">
                            <div class="permission-group">
                                <span class="group-title">数据查看</span>
                                <span class="permission-tag read">用户查看</span>
                                <span class="permission-tag read">业务数据</span>
                                <span class="permission-tag read">监控数据</span>
                                <span class="permission-tag read">日志查看</span>
                            </div>
                        </div>
                        <div class="role-actions">
                            <button class="btn btn-sm btn-info">
                                <i class="fas fa-edit"></i> 编辑权限
                            </button>
                            <button class="btn btn-sm btn-success">
                                <i class="fas fa-user-plus"></i> 分配用户
                            </button>
                            <button class="btn btn-sm btn-warning">
                                <i class="fas fa-copy"></i> 复制角色
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 角色权限矩阵 -->
                <div class="role-permission-matrix">
                    <h3>角色权限矩阵</h3>
                    <div class="matrix-table">
                        <table class="permission-matrix">
                            <thead>
                                <tr>
                                    <th>权限/角色</th>
                                    <th>系统管理员</th>
                                    <th>业务操作员</th>
                                    <th>只读用户</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>用户管理</td>
                                    <td><i class="fas fa-check-circle granted"></i></td>
                                    <td><i class="fas fa-times-circle denied"></i></td>
                                    <td><i class="fas fa-eye read-only"></i></td>
                                </tr>
                                <tr>
                                    <td>角色管理</td>
                                    <td><i class="fas fa-check-circle granted"></i></td>
                                    <td><i class="fas fa-times-circle denied"></i></td>
                                    <td><i class="fas fa-times-circle denied"></i></td>
                                </tr>
                                <tr>
                                    <td>API管理</td>
                                    <td><i class="fas fa-check-circle granted"></i></td>
                                    <td><i class="fas fa-edit partial"></i></td>
                                    <td><i class="fas fa-eye read-only"></i></td>
                                </tr>
                                <tr>
                                    <td>业务逻辑</td>
                                    <td><i class="fas fa-check-circle granted"></i></td>
                                    <td><i class="fas fa-eye read-only"></i></td>
                                    <td><i class="fas fa-eye read-only"></i></td>
                                </tr>
                                <tr>
                                    <td>监控数据</td>
                                    <td><i class="fas fa-check-circle granted"></i></td>
                                    <td><i class="fas fa-eye read-only"></i></td>
                                    <td><i class="fas fa-eye read-only"></i></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            `
        },
        'permissions': {
            title: 'RBAC权限管理',
            content: `
                <div class="page-header">
                    <h2>RBAC权限管理</h2>
                    <div class="header-actions">
                        <button class="btn btn-primary">
                            <i class="fas fa-plus"></i> 新增权限
                        </button>
                        <button class="btn btn-secondary">
                            <i class="fas fa-sync"></i> 刷新权限树
                        </button>
                    </div>
                </div>

                <!-- RBAC概览 -->
                <div class="rbac-overview">
                    <div class="rbac-card">
                        <div class="rbac-icon">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="rbac-info">
                            <h4>用户 (Users)</h4>
                            <p>系统中的实际使用者</p>
                            <span class="count">156 个用户</span>
                        </div>
                    </div>
                    <div class="rbac-card">
                        <div class="rbac-icon">
                            <i class="fas fa-user-tag"></i>
                        </div>
                        <div class="rbac-info">
                            <h4>角色 (Roles)</h4>
                            <p>权限的集合，分配给用户</p>
                            <span class="count">12 个角色</span>
                        </div>
                    </div>
                    <div class="rbac-card">
                        <div class="rbac-icon">
                            <i class="fas fa-key"></i>
                        </div>
                        <div class="rbac-info">
                            <h4>权限 (Permissions)</h4>
                            <p>对资源的操作许可</p>
                            <span class="count">89 个权限</span>
                        </div>
                    </div>
                    <div class="rbac-card">
                        <div class="rbac-icon">
                            <i class="fas fa-cube"></i>
                        </div>
                        <div class="rbac-info">
                            <h4>资源 (Resources)</h4>
                            <p>系统中的功能模块</p>
                            <span class="count">23 个资源</span>
                        </div>
                    </div>
                </div>

                <!-- RBAC关系图 -->
                <div class="rbac-relationship">
                    <h3>RBAC权限模型关系</h3>
                    <div class="relationship-diagram">
                        <div class="rbac-entity user-entity">
                            <i class="fas fa-user"></i>
                            <span>用户</span>
                        </div>
                        <div class="relationship-arrow">
                            <span>分配</span>
                            <i class="fas fa-arrow-right"></i>
                        </div>
                        <div class="rbac-entity role-entity">
                            <i class="fas fa-user-tag"></i>
                            <span>角色</span>
                        </div>
                        <div class="relationship-arrow">
                            <span>拥有</span>
                            <i class="fas fa-arrow-right"></i>
                        </div>
                        <div class="rbac-entity permission-entity">
                            <i class="fas fa-key"></i>
                            <span>权限</span>
                        </div>
                        <div class="relationship-arrow">
                            <span>操作</span>
                            <i class="fas fa-arrow-right"></i>
                        </div>
                        <div class="rbac-entity resource-entity">
                            <i class="fas fa-cube"></i>
                            <span>资源</span>
                        </div>
                    </div>
                </div>

                <!-- 权限树结构 -->
                <div class="permission-tree-section">
                    <h3>权限树结构</h3>
                    <div class="permission-tree">
                        <div class="tree-node expanded">
                            <i class="fas fa-folder-open"></i>
                            <span>系统管理</span>
                            <span class="resource-code">system</span>
                            <div class="tree-children">
                                <div class="tree-leaf">
                                    <i class="fas fa-file"></i>
                                    <span>用户管理</span>
                                    <div class="permission-actions">
                                        <span class="action-tag create">user:create</span>
                                        <span class="action-tag read">user:read</span>
                                        <span class="action-tag update">user:update</span>
                                        <span class="action-tag delete">user:delete</span>
                                    </div>
                                </div>
                                <div class="tree-leaf">
                                    <i class="fas fa-file"></i>
                                    <span>角色管理</span>
                                    <div class="permission-actions">
                                        <span class="action-tag create">role:create</span>
                                        <span class="action-tag read">role:read</span>
                                        <span class="action-tag update">role:update</span>
                                        <span class="action-tag delete">role:delete</span>
                                    </div>
                                </div>
                                <div class="tree-leaf">
                                    <i class="fas fa-file"></i>
                                    <span>权限管理</span>
                                    <div class="permission-actions">
                                        <span class="action-tag create">permission:create</span>
                                        <span class="action-tag read">permission:read</span>
                                        <span class="action-tag update">permission:update</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="tree-node expanded">
                            <i class="fas fa-folder-open"></i>
                            <span>业务管理</span>
                            <span class="resource-code">business</span>
                            <div class="tree-children">
                                <div class="tree-leaf">
                                    <i class="fas fa-file"></i>
                                    <span>API管理</span>
                                    <div class="permission-actions">
                                        <span class="action-tag create">api:create</span>
                                        <span class="action-tag read">api:read</span>
                                        <span class="action-tag update">api:update</span>
                                        <span class="action-tag delete">api:delete</span>
                                        <span class="action-tag execute">api:execute</span>
                                    </div>
                                </div>
                                <div class="tree-leaf">
                                    <i class="fas fa-file"></i>
                                    <span>业务逻辑</span>
                                    <div class="permission-actions">
                                        <span class="action-tag read">business:read</span>
                                        <span class="action-tag update">business:update</span>
                                        <span class="action-tag execute">business:execute</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="tree-node expanded">
                            <i class="fas fa-folder-open"></i>
                            <span>监控管理</span>
                            <span class="resource-code">monitor</span>
                            <div class="tree-children">
                                <div class="tree-leaf">
                                    <i class="fas fa-file"></i>
                                    <span>性能监控</span>
                                    <div class="permission-actions">
                                        <span class="action-tag read">monitor:read</span>
                                        <span class="action-tag update">monitor:config</span>
                                    </div>
                                </div>
                                <div class="tree-leaf">
                                    <i class="fas fa-file"></i>
                                    <span>日志管理</span>
                                    <div class="permission-actions">
                                        <span class="action-tag read">log:read</span>
                                        <span class="action-tag delete">log:delete</span>
                                        <span class="action-tag export">log:export</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `
        },
        'system-params': {
            title: '系统参数配置',
            content: `
                <div class="page-header">
                    <h2>系统参数配置</h2>
                    <div class="header-actions">
                        <button class="btn btn-primary">
                            <i class="fas fa-plus"></i> 新增参数
                        </button>
                        <button class="btn btn-success">
                            <i class="fas fa-save"></i> 批量保存
                        </button>
                        <button class="btn btn-warning">
                            <i class="fas fa-sync"></i> 重载配置
                        </button>
                        <button class="btn btn-info">
                            <i class="fas fa-download"></i> 导出配置
                        </button>
                    </div>
                </div>

                <!-- 配置统计 -->
                <div class="config-stats">
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-cogs"></i>
                        </div>
                        <div class="stat-content">
                            <h4>总参数数</h4>
                            <span class="stat-number">45</span>
                            <small>系统配置参数</small>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <div class="stat-content">
                            <h4>已启用</h4>
                            <span class="stat-number">42</span>
                            <small>正常使用中</small>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="stat-content">
                            <h4>待更新</h4>
                            <span class="stat-number">3</span>
                            <small>需要重启生效</small>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-clock"></i>
                        </div>
                        <div class="stat-content">
                            <h4>最后更新</h4>
                            <span class="stat-number">2小时前</span>
                            <small>配置修改时间</small>
                        </div>
                    </div>
                </div>

                <!-- 搜索和筛选 -->
                <div class="search-filters">
                    <div class="search-box">
                        <i class="fas fa-search"></i>
                        <input type="text" placeholder="搜索参数名称、描述..." class="search-input">
                    </div>
                    <div class="filter-group">
                        <select class="filter-select">
                            <option>所有分类</option>
                            <option>数据库配置</option>
                            <option>缓存配置</option>
                            <option>安全配置</option>
                            <option>业务配置</option>
                        </select>
                        <select class="filter-select">
                            <option>所有状态</option>
                            <option>启用</option>
                            <option>禁用</option>
                            <option>待更新</option>
                        </select>
                    </div>
                </div>

                <!-- 配置参数表格 -->
                <div class="table-container">
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th><input type="checkbox" class="select-all"></th>
                                <th>参数名称</th>
                                <th>参数值</th>
                                <th>参数描述</th>
                                <th>分类</th>
                                <th>状态</th>
                                <th>最后修改</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><input type="checkbox"></td>
                                <td>
                                    <div class="param-info">
                                        <strong>database.pool.size</strong>
                                        <small class="param-key">DB_POOL_SIZE</small>
                                    </div>
                                </td>
                                <td>
                                    <div class="param-value">
                                        <input type="number" value="20" class="value-input" data-original="20">
                                        <span class="value-type">Integer</span>
                                    </div>
                                </td>
                                <td>数据库连接池最大连接数</td>
                                <td><span class="category-tag database">数据库配置</span></td>
                                <td><span class="status-badge active">启用</span></td>
                                <td>2024-01-20 10:30:00</td>
                                <td>
                                    <div class="action-buttons">
                                        <button class="btn btn-sm btn-info" title="编辑">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="btn btn-sm btn-success" title="保存">
                                            <i class="fas fa-save"></i>
                                        </button>
                                        <button class="btn btn-sm btn-warning" title="重置">
                                            <i class="fas fa-undo"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td><input type="checkbox"></td>
                                <td>
                                    <div class="param-info">
                                        <strong>redis.connection.timeout</strong>
                                        <small class="param-key">REDIS_TIMEOUT</small>
                                    </div>
                                </td>
                                <td>
                                    <div class="param-value">
                                        <input type="number" value="5000" class="value-input" data-original="5000">
                                        <span class="value-type">Long</span>
                                    </div>
                                </td>
                                <td>Redis连接超时时间(毫秒)</td>
                                <td><span class="category-tag cache">缓存配置</span></td>
                                <td><span class="status-badge active">启用</span></td>
                                <td>2024-01-19 15:20:00</td>
                                <td>
                                    <div class="action-buttons">
                                        <button class="btn btn-sm btn-info" title="编辑">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="btn btn-sm btn-success" title="保存">
                                            <i class="fas fa-save"></i>
                                        </button>
                                        <button class="btn btn-sm btn-warning" title="重置">
                                            <i class="fas fa-undo"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td><input type="checkbox"></td>
                                <td>
                                    <div class="param-info">
                                        <strong>security.jwt.expiration</strong>
                                        <small class="param-key">JWT_EXPIRATION</small>
                                    </div>
                                </td>
                                <td>
                                    <div class="param-value">
                                        <input type="text" value="7200" class="value-input modified" data-original="3600">
                                        <span class="value-type">String</span>
                                    </div>
                                </td>
                                <td>JWT令牌过期时间(秒)</td>
                                <td><span class="category-tag security">安全配置</span></td>
                                <td><span class="status-badge pending">待更新</span></td>
                                <td>2024-01-20 09:15:00</td>
                                <td>
                                    <div class="action-buttons">
                                        <button class="btn btn-sm btn-info" title="编辑">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="btn btn-sm btn-success" title="保存">
                                            <i class="fas fa-save"></i>
                                        </button>
                                        <button class="btn btn-sm btn-warning" title="重置">
                                            <i class="fas fa-undo"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td><input type="checkbox"></td>
                                <td>
                                    <div class="param-info">
                                        <strong>business.max.retry.count</strong>
                                        <small class="param-key">MAX_RETRY_COUNT</small>
                                    </div>
                                </td>
                                <td>
                                    <div class="param-value">
                                        <input type="number" value="3" class="value-input" data-original="3">
                                        <span class="value-type">Integer</span>
                                    </div>
                                </td>
                                <td>业务操作最大重试次数</td>
                                <td><span class="category-tag business">业务配置</span></td>
                                <td><span class="status-badge active">启用</span></td>
                                <td>2024-01-18 14:45:00</td>
                                <td>
                                    <div class="action-buttons">
                                        <button class="btn btn-sm btn-info" title="编辑">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="btn btn-sm btn-success" title="保存">
                                            <i class="fas fa-save"></i>
                                        </button>
                                        <button class="btn btn-sm btn-warning" title="重置">
                                            <i class="fas fa-undo"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <!-- 分页 -->
                <div class="pagination">
                    <button class="btn btn-sm">上一页</button>
                    <span class="page-info">第 1 页，共 5 页</span>
                    <button class="btn btn-sm">下一页</button>
                </div>
            `
        },
        'dictionary': {
            title: '字典管理',
            content: `
                <div class="page-header">
                    <h2>字典管理</h2>
                    <div class="header-actions">
                        <button class="btn btn-primary">
                            <i class="fas fa-plus"></i> 新增字典
                        </button>
                        <button class="btn btn-success">
                            <i class="fas fa-sync"></i> 刷新缓存
                        </button>
                        <button class="btn btn-info">
                            <i class="fas fa-download"></i> 导出字典
                        </button>
                    </div>
                </div>

                <!-- 字典统计 -->
                <div class="dict-stats">
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-book"></i>
                        </div>
                        <div class="stat-content">
                            <h4>字典类型</h4>
                            <span class="stat-number">12</span>
                            <small>业务字典分类</small>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-list"></i>
                        </div>
                        <div class="stat-content">
                            <h4>字典项目</h4>
                            <span class="stat-number">156</span>
                            <small>总字典条目数</small>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <div class="stat-content">
                            <h4>启用状态</h4>
                            <span class="stat-number">142</span>
                            <small>正常使用中</small>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-clock"></i>
                        </div>
                        <div class="stat-content">
                            <h4>缓存状态</h4>
                            <span class="stat-number">已同步</span>
                            <small>5分钟前更新</small>
                        </div>
                    </div>
                </div>

                <!-- 搜索和筛选 -->
                <div class="search-filters">
                    <div class="search-box">
                        <i class="fas fa-search"></i>
                        <input type="text" placeholder="搜索字典类型、名称..." class="search-input">
                    </div>
                    <div class="filter-group">
                        <select class="filter-select">
                            <option>所有类型</option>
                            <option>系统字典</option>
                            <option>业务字典</option>
                            <option>配置字典</option>
                        </select>
                        <select class="filter-select">
                            <option>所有状态</option>
                            <option>启用</option>
                            <option>禁用</option>
                        </select>
                    </div>
                </div>

                <!-- 字典类型表格 -->
                <div class="table-container">
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th><input type="checkbox" class="select-all"></th>
                                <th>字典类型</th>
                                <th>字典名称</th>
                                <th>字典项数量</th>
                                <th>类型分类</th>
                                <th>状态</th>
                                <th>创建时间</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><input type="checkbox"></td>
                                <td>
                                    <div class="dict-type-info">
                                        <strong>user_status</strong>
                                        <small class="dict-code">USER_STATUS</small>
                                    </div>
                                </td>
                                <td>用户状态</td>
                                <td>
                                    <div class="item-count">
                                        <span class="count-number">4</span>
                                        <small>个字典项</small>
                                    </div>
                                </td>
                                <td><span class="type-tag system">系统字典</span></td>
                                <td><span class="status-badge active">启用</span></td>
                                <td>2024-01-15 10:30:00</td>
                                <td>
                                    <div class="action-buttons">
                                        <button class="btn btn-sm btn-info" title="查看详情">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="btn btn-sm btn-warning" title="编辑">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="btn btn-sm btn-success" title="管理字典项">
                                            <i class="fas fa-list"></i>
                                        </button>
                                        <button class="btn btn-sm btn-danger" title="删除">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td><input type="checkbox"></td>
                                <td>
                                    <div class="dict-type-info">
                                        <strong>order_status</strong>
                                        <small class="dict-code">ORDER_STATUS</small>
                                    </div>
                                </td>
                                <td>订单状态</td>
                                <td>
                                    <div class="item-count">
                                        <span class="count-number">8</span>
                                        <small>个字典项</small>
                                    </div>
                                </td>
                                <td><span class="type-tag business">业务字典</span></td>
                                <td><span class="status-badge active">启用</span></td>
                                <td>2024-01-16 14:20:00</td>
                                <td>
                                    <div class="action-buttons">
                                        <button class="btn btn-sm btn-info" title="查看详情">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="btn btn-sm btn-warning" title="编辑">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="btn btn-sm btn-success" title="管理字典项">
                                            <i class="fas fa-list"></i>
                                        </button>
                                        <button class="btn btn-sm btn-danger" title="删除">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <!-- 分页 -->
                <div class="pagination">
                    <button class="btn btn-sm">上一页</button>
                    <span class="page-info">第 1 页，共 3 页</span>
                    <button class="btn btn-sm">下一页</button>
                </div>
            `
        },
        'api-management': {
            title: 'API管理',
            content: `
                <div class="page-header">
                    <h2>API接口管理</h2>
                    <div class="header-actions">
                        <button class="btn btn-primary">
                            <i class="fas fa-plus"></i> 新增接口
                        </button>
                        <button class="btn btn-success">
                            <i class="fas fa-sync"></i> 同步接口
                        </button>
                        <button class="btn btn-warning">
                            <i class="fas fa-test"></i> 批量测试
                        </button>
                        <button class="btn btn-info">
                            <i class="fas fa-download"></i> 导出文档
                        </button>
                    </div>
                </div>

                <!-- API统计 -->
                <div class="api-stats">
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-code"></i>
                        </div>
                        <div class="stat-content">
                            <h4>总接口数</h4>
                            <span class="stat-number">156</span>
                            <small>已注册接口</small>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <div class="stat-content">
                            <h4>正常接口</h4>
                            <span class="stat-number">142</span>
                            <small>运行正常</small>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="stat-content">
                            <h4>异常接口</h4>
                            <span class="stat-number">3</span>
                            <small>需要处理</small>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-clock"></i>
                        </div>
                        <div class="stat-content">
                            <h4>平均响应</h4>
                            <span class="stat-number">125ms</span>
                            <small>接口响应时间</small>
                        </div>
                    </div>
                </div>

                <!-- 搜索和筛选 -->
                <div class="search-filters">
                    <div class="search-box">
                        <i class="fas fa-search"></i>
                        <input type="text" placeholder="搜索接口路径、描述..." class="search-input">
                    </div>
                    <div class="filter-group">
                        <select class="filter-select">
                            <option>所有方法</option>
                            <option>GET</option>
                            <option>POST</option>
                            <option>PUT</option>
                            <option>DELETE</option>
                        </select>
                        <select class="filter-select">
                            <option>所有状态</option>
                            <option>正常</option>
                            <option>异常</option>
                            <option>维护中</option>
                        </select>
                        <select class="filter-select">
                            <option>所有模块</option>
                            <option>用户管理</option>
                            <option>权限管理</option>
                            <option>业务管理</option>
                        </select>
                    </div>
                </div>

                <!-- API接口表格 -->
                <div class="table-container">
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th><input type="checkbox" class="select-all"></th>
                                <th>接口信息</th>
                                <th>请求方法</th>
                                <th>所属模块</th>
                                <th>响应时间</th>
                                <th>调用次数</th>
                                <th>状态</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><input type="checkbox"></td>
                                <td>
                                    <div class="api-info">
                                        <strong>/api/users</strong>
                                        <small class="api-desc">获取用户列表接口</small>
                                    </div>
                                </td>
                                <td><span class="method-badge get">GET</span></td>
                                <td><span class="module-tag user">用户管理</span></td>
                                <td>
                                    <div class="response-time">
                                        <span class="time-value">85ms</span>
                                        <small class="time-status good">良好</small>
                                    </div>
                                </td>
                                <td>
                                    <div class="call-count">
                                        <span class="count-number">1,245</span>
                                        <small>今日调用</small>
                                    </div>
                                </td>
                                <td><span class="status-badge active">正常</span></td>
                                <td>
                                    <div class="action-buttons">
                                        <button class="btn btn-sm btn-info" title="查看详情">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="btn btn-sm btn-success" title="测试接口">
                                            <i class="fas fa-play"></i>
                                        </button>
                                        <button class="btn btn-sm btn-warning" title="编辑">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="btn btn-sm btn-secondary" title="文档">
                                            <i class="fas fa-book"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td><input type="checkbox"></td>
                                <td>
                                    <div class="api-info">
                                        <strong>/api/users</strong>
                                        <small class="api-desc">创建新用户接口</small>
                                    </div>
                                </td>
                                <td><span class="method-badge post">POST</span></td>
                                <td><span class="module-tag user">用户管理</span></td>
                                <td>
                                    <div class="response-time">
                                        <span class="time-value">156ms</span>
                                        <small class="time-status normal">正常</small>
                                    </div>
                                </td>
                                <td>
                                    <div class="call-count">
                                        <span class="count-number">89</span>
                                        <small>今日调用</small>
                                    </div>
                                </td>
                                <td><span class="status-badge active">正常</span></td>
                                <td>
                                    <div class="action-buttons">
                                        <button class="btn btn-sm btn-info" title="查看详情">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="btn btn-sm btn-success" title="测试接口">
                                            <i class="fas fa-play"></i>
                                        </button>
                                        <button class="btn btn-sm btn-warning" title="编辑">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="btn btn-sm btn-secondary" title="文档">
                                            <i class="fas fa-book"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <!-- 分页 -->
                <div class="pagination">
                    <button class="btn btn-sm">上一页</button>
                    <span class="page-info">第 1 页，共 12 页</span>
                    <button class="btn btn-sm">下一页</button>
                </div>
            `
        },
        'task-monitor': {
            title: '任务监控',
            content: `
                <div class="page-header">
                    <h2>批量任务监控</h2>
                    <div class="header-actions">
                        <button class="btn btn-success">
                            <i class="fas fa-play"></i> 启动任务
                        </button>
                        <button class="btn btn-danger">
                            <i class="fas fa-stop"></i> 停止任务
                        </button>
                    </div>
                </div>
                <div class="task-overview">
                    <div class="task-stat">
                        <h4>运行中</h4>
                        <span class="stat-number running">5</span>
                    </div>
                    <div class="task-stat">
                        <h4>等待中</h4>
                        <span class="stat-number waiting">12</span>
                    </div>
                    <div class="task-stat">
                        <h4>已完成</h4>
                        <span class="stat-number completed">234</span>
                    </div>
                    <div class="task-stat">
                        <h4>失败</h4>
                        <span class="stat-number failed">3</span>
                    </div>
                </div>
                <div class="task-list">
                    <div class="task-item">
                        <div class="task-info">
                            <h4>数据同步任务</h4>
                            <p>同步用户数据到外部系统</p>
                        </div>
                        <div class="task-progress">
                            <div class="progress-bar">
                                <div class="progress-fill" style="width: 75%"></div>
                            </div>
                            <span>75%</span>
                        </div>
                        <div class="task-status">
                            <span class="status-badge running">运行中</span>
                        </div>
                    </div>
                    <div class="task-item">
                        <div class="task-info">
                            <h4>报表生成任务</h4>
                            <p>生成月度业务报表</p>
                        </div>
                        <div class="task-progress">
                            <div class="progress-bar">
                                <div class="progress-fill" style="width: 100%"></div>
                            </div>
                            <span>100%</span>
                        </div>
                        <div class="task-status">
                            <span class="status-badge completed">已完成</span>
                        </div>
                    </div>
                </div>
            `
        },
        'performance': {
            title: '性能监控',
            content: `
                <div class="page-header">
                    <h2>系统性能监控</h2>
                    <div class="time-range">
                        <select class="form-control">
                            <option>最近1小时</option>
                            <option>最近24小时</option>
                            <option>最近7天</option>
                        </select>
                    </div>
                </div>
                <div class="performance-metrics">
                    <div class="metric-card">
                        <h4>CPU使用率</h4>
                        <div class="metric-value">
                            <span class="value">45%</span>
                            <div class="metric-chart">
                                <div class="chart-bar" style="height: 45%"></div>
                            </div>
                        </div>
                    </div>
                    <div class="metric-card">
                        <h4>内存使用率</h4>
                        <div class="metric-value">
                            <span class="value">68%</span>
                            <div class="metric-chart">
                                <div class="chart-bar" style="height: 68%"></div>
                            </div>
                        </div>
                    </div>
                    <div class="metric-card">
                        <h4>磁盘使用率</h4>
                        <div class="metric-value">
                            <span class="value">32%</span>
                            <div class="metric-chart">
                                <div class="chart-bar" style="height: 32%"></div>
                            </div>
                        </div>
                    </div>
                    <div class="metric-card">
                        <h4>网络吞吐量</h4>
                        <div class="metric-value">
                            <span class="value">1.2GB/s</span>
                            <div class="metric-chart">
                                <div class="chart-bar" style="height: 60%"></div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="service-status">
                    <h3>服务状态</h3>
                    <div class="service-list">
                        <div class="service-item">
                            <span class="service-name">bms-api</span>
                            <span class="status-badge active">运行中</span>
                            <span class="service-info">响应时间: 45ms</span>
                        </div>
                        <div class="service-item">
                            <span class="service-name">bms-manage</span>
                            <span class="status-badge active">运行中</span>
                            <span class="service-info">响应时间: 32ms</span>
                        </div>
                        <div class="service-item">
                            <span class="service-name">bms-biz</span>
                            <span class="status-badge active">运行中</span>
                            <span class="service-info">响应时间: 28ms</span>
                        </div>
                    </div>
                </div>
            `
        },
        'business-logic': {
            title: '业务逻辑管理',
            content: `
                <div class="page-header">
                    <h2>业务逻辑管理</h2>
                    <div class="header-actions">
                        <button class="btn btn-primary">
                            <i class="fas fa-plus"></i> 新增业务规则
                        </button>
                        <button class="btn btn-success">
                            <i class="fas fa-sync"></i> 刷新规则
                        </button>
                        <button class="btn btn-warning">
                            <i class="fas fa-test"></i> 规则测试
                        </button>
                        <button class="btn btn-info">
                            <i class="fas fa-download"></i> 导出配置
                        </button>
                    </div>
                </div>

                <!-- 业务统计 -->
                <div class="business-stats">
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-cogs"></i>
                        </div>
                        <div class="stat-content">
                            <h4>业务规则</h4>
                            <span class="stat-number">28</span>
                            <small>已配置规则</small>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <div class="stat-content">
                            <h4>启用规则</h4>
                            <span class="stat-number">25</span>
                            <small>正在运行</small>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="stat-content">
                            <h4>异常规则</h4>
                            <span class="stat-number">1</span>
                            <small>需要处理</small>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <div class="stat-content">
                            <h4>执行次数</h4>
                            <span class="stat-number">12.5K</span>
                            <small>今日执行</small>
                        </div>
                    </div>
                </div>

                <!-- 搜索和筛选 -->
                <div class="search-filters">
                    <div class="search-box">
                        <i class="fas fa-search"></i>
                        <input type="text" placeholder="搜索规则名称、描述..." class="search-input">
                    </div>
                    <div class="filter-group">
                        <select class="filter-select">
                            <option>所有类型</option>
                            <option>验证规则</option>
                            <option>计算规则</option>
                            <option>流程规则</option>
                            <option>通知规则</option>
                        </select>
                        <select class="filter-select">
                            <option>所有状态</option>
                            <option>启用</option>
                            <option>禁用</option>
                            <option>测试中</option>
                        </select>
                    </div>
                </div>

                <!-- 业务规则表格 -->
                <div class="table-container">
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th><input type="checkbox" class="select-all"></th>
                                <th>规则信息</th>
                                <th>规则类型</th>
                                <th>触发条件</th>
                                <th>执行次数</th>
                                <th>成功率</th>
                                <th>状态</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><input type="checkbox"></td>
                                <td>
                                    <div class="rule-info">
                                        <strong>用户注册验证</strong>
                                        <small class="rule-desc">验证用户注册信息的完整性和有效性</small>
                                    </div>
                                </td>
                                <td><span class="rule-type validation">验证规则</span></td>
                                <td>
                                    <div class="trigger-condition">
                                        <span class="condition-text">用户注册时</span>
                                        <small class="condition-detail">POST /api/users</small>
                                    </div>
                                </td>
                                <td>
                                    <div class="execution-count">
                                        <span class="count-number">1,245</span>
                                        <small>今日执行</small>
                                    </div>
                                </td>
                                <td>
                                    <div class="success-rate">
                                        <span class="rate-value">98.5%</span>
                                        <small class="rate-status good">优秀</small>
                                    </div>
                                </td>
                                <td><span class="status-badge active">启用</span></td>
                                <td>
                                    <div class="action-buttons">
                                        <button class="btn btn-sm btn-info" title="查看详情">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="btn btn-sm btn-success" title="测试规则">
                                            <i class="fas fa-play"></i>
                                        </button>
                                        <button class="btn btn-sm btn-warning" title="编辑">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="btn btn-sm btn-secondary" title="日志">
                                            <i class="fas fa-list"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td><input type="checkbox"></td>
                                <td>
                                    <div class="rule-info">
                                        <strong>订单金额计算</strong>
                                        <small class="rule-desc">根据商品价格和优惠券计算订单总金额</small>
                                    </div>
                                </td>
                                <td><span class="rule-type calculation">计算规则</span></td>
                                <td>
                                    <div class="trigger-condition">
                                        <span class="condition-text">订单创建时</span>
                                        <small class="condition-detail">POST /api/orders</small>
                                    </div>
                                </td>
                                <td>
                                    <div class="execution-count">
                                        <span class="count-number">856</span>
                                        <small>今日执行</small>
                                    </div>
                                </td>
                                <td>
                                    <div class="success-rate">
                                        <span class="rate-value">99.2%</span>
                                        <small class="rate-status excellent">卓越</small>
                                    </div>
                                </td>
                                <td><span class="status-badge active">启用</span></td>
                                <td>
                                    <div class="action-buttons">
                                        <button class="btn btn-sm btn-info" title="查看详情">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="btn btn-sm btn-success" title="测试规则">
                                            <i class="fas fa-play"></i>
                                        </button>
                                        <button class="btn btn-sm btn-warning" title="编辑">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="btn btn-sm btn-secondary" title="日志">
                                            <i class="fas fa-list"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <!-- 分页 -->
                <div class="pagination">
                    <button class="btn btn-sm">上一页</button>
                    <span class="page-info">第 1 页，共 6 页</span>
                    <button class="btn btn-sm">下一页</button>
                </div>
            `
        },
        'external-systems': {
            title: '外部系统管理',
            content: `
                <div class="page-header">
                    <h2>外部系统集成管理</h2>
                    <div class="header-actions">
                        <button class="btn btn-primary">
                            <i class="fas fa-plus"></i> 新增系统
                        </button>
                        <button class="btn btn-success">
                            <i class="fas fa-sync"></i> 同步配置
                        </button>
                        <button class="btn btn-warning">
                            <i class="fas fa-test"></i> 连接测试
                        </button>
                        <button class="btn btn-info">
                            <i class="fas fa-download"></i> 导出配置
                        </button>
                    </div>
                </div>

                <!-- 外部系统统计 -->
                <div class="external-stats">
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-network-wired"></i>
                        </div>
                        <div class="stat-content">
                            <h4>接入系统</h4>
                            <span class="stat-number">12</span>
                            <small>已集成系统</small>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <div class="stat-content">
                            <h4>正常连接</h4>
                            <span class="stat-number">10</span>
                            <small>连接正常</small>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="stat-content">
                            <h4>异常连接</h4>
                            <span class="stat-number">2</span>
                            <small>需要处理</small>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-exchange-alt"></i>
                        </div>
                        <div class="stat-content">
                            <h4>数据同步</h4>
                            <span class="stat-number">8.5K</span>
                            <small>今日同步量</small>
                        </div>
                    </div>
                </div>

                <!-- 搜索和筛选 -->
                <div class="search-filters">
                    <div class="search-box">
                        <i class="fas fa-search"></i>
                        <input type="text" placeholder="搜索系统名称、描述..." class="search-input">
                    </div>
                    <div class="filter-group">
                        <select class="filter-select">
                            <option>所有类型</option>
                            <option>数据库系统</option>
                            <option>业务系统</option>
                            <option>监控系统</option>
                            <option>第三方API</option>
                        </select>
                        <select class="filter-select">
                            <option>所有状态</option>
                            <option>正常</option>
                            <option>异常</option>
                            <option>维护中</option>
                        </select>
                    </div>
                </div>

                <!-- 外部系统表格 -->
                <div class="table-container">
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th><input type="checkbox" class="select-all"></th>
                                <th>系统信息</th>
                                <th>系统类型</th>
                                <th>连接地址</th>
                                <th>连接状态</th>
                                <th>最后同步</th>
                                <th>数据量</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><input type="checkbox"></td>
                                <td>
                                    <div class="system-info">
                                        <strong>用户中心系统</strong>
                                        <small class="system-desc">统一用户认证和授权系统</small>
                                    </div>
                                </td>
                                <td><span class="system-type business">业务系统</span></td>
                                <td>
                                    <div class="connection-info">
                                        <span class="connection-url">https://user.example.com</span>
                                        <small class="connection-protocol">HTTPS</small>
                                    </div>
                                </td>
                                <td><span class="status-badge active">正常</span></td>
                                <td>
                                    <div class="sync-time">
                                        <span class="time-value">2分钟前</span>
                                        <small class="sync-status success">成功</small>
                                    </div>
                                </td>
                                <td>
                                    <div class="data-volume">
                                        <span class="volume-number">1.2K</span>
                                        <small>用户记录</small>
                                    </div>
                                </td>
                                <td>
                                    <div class="action-buttons">
                                        <button class="btn btn-sm btn-info" title="查看详情">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="btn btn-sm btn-success" title="测试连接">
                                            <i class="fas fa-plug"></i>
                                        </button>
                                        <button class="btn btn-sm btn-warning" title="编辑">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="btn btn-sm btn-secondary" title="同步">
                                            <i class="fas fa-sync"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td><input type="checkbox"></td>
                                <td>
                                    <div class="system-info">
                                        <strong>订单管理系统</strong>
                                        <small class="system-desc">电商订单处理和管理系统</small>
                                    </div>
                                </td>
                                <td><span class="system-type business">业务系统</span></td>
                                <td>
                                    <div class="connection-info">
                                        <span class="connection-url">https://order.example.com</span>
                                        <small class="connection-protocol">HTTPS</small>
                                    </div>
                                </td>
                                <td><span class="status-badge active">正常</span></td>
                                <td>
                                    <div class="sync-time">
                                        <span class="time-value">5分钟前</span>
                                        <small class="sync-status success">成功</small>
                                    </div>
                                </td>
                                <td>
                                    <div class="data-volume">
                                        <span class="volume-number">3.8K</span>
                                        <small>订单记录</small>
                                    </div>
                                </td>
                                <td>
                                    <div class="action-buttons">
                                        <button class="btn btn-sm btn-info" title="查看详情">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="btn btn-sm btn-success" title="测试连接">
                                            <i class="fas fa-plug"></i>
                                        </button>
                                        <button class="btn btn-sm btn-warning" title="编辑">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="btn btn-sm btn-secondary" title="同步">
                                            <i class="fas fa-sync"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <!-- 分页 -->
                <div class="pagination">
                    <button class="btn btn-sm">上一页</button>
                    <span class="page-info">第 1 页，共 3 页</span>
                    <button class="btn btn-sm">下一页</button>
                </div>
            `
        },
        'task-config': {
            title: '任务配置管理',
            content: `
                <div class="page-header">
                    <h2>批量任务配置</h2>
                    <div class="header-actions">
                        <button class="btn btn-primary">
                            <i class="fas fa-plus"></i> 新增任务
                        </button>
                        <button class="btn btn-success">
                            <i class="fas fa-play"></i> 批量启动
                        </button>
                        <button class="btn btn-warning">
                            <i class="fas fa-pause"></i> 批量暂停
                        </button>
                        <button class="btn btn-info">
                            <i class="fas fa-download"></i> 导出配置
                        </button>
                    </div>
                </div>

                <!-- 任务统计 -->
                <div class="task-stats">
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-tasks"></i>
                        </div>
                        <div class="stat-content">
                            <h4>总任务数</h4>
                            <span class="stat-number">24</span>
                            <small>已配置任务</small>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-play-circle"></i>
                        </div>
                        <div class="stat-content">
                            <h4>运行中</h4>
                            <span class="stat-number">8</span>
                            <small>正在执行</small>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-pause-circle"></i>
                        </div>
                        <div class="stat-content">
                            <h4>已暂停</h4>
                            <span class="stat-number">3</span>
                            <small>暂停执行</small>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <div class="stat-content">
                            <h4>执行成功</h4>
                            <span class="stat-number">95.8%</span>
                            <small>成功率</small>
                        </div>
                    </div>
                </div>

                <!-- 搜索和筛选 -->
                <div class="search-filters">
                    <div class="search-box">
                        <i class="fas fa-search"></i>
                        <input type="text" placeholder="搜索任务名称、描述..." class="search-input">
                    </div>
                    <div class="filter-group">
                        <select class="filter-select">
                            <option>所有类型</option>
                            <option>数据同步</option>
                            <option>报表生成</option>
                            <option>数据清理</option>
                            <option>系统维护</option>
                        </select>
                        <select class="filter-select">
                            <option>所有状态</option>
                            <option>运行中</option>
                            <option>已暂停</option>
                            <option>已停止</option>
                        </select>
                    </div>
                </div>

                <!-- 任务配置表格 -->
                <div class="table-container">
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th><input type="checkbox" class="select-all"></th>
                                <th>任务信息</th>
                                <th>任务类型</th>
                                <th>执行计划</th>
                                <th>下次执行</th>
                                <th>执行状态</th>
                                <th>成功率</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><input type="checkbox"></td>
                                <td>
                                    <div class="task-info">
                                        <strong>用户数据同步</strong>
                                        <small class="task-desc">同步用户信息到外部系统</small>
                                    </div>
                                </td>
                                <td><span class="task-type sync">数据同步</span></td>
                                <td>
                                    <div class="schedule-info">
                                        <span class="schedule-text">每日 02:00</span>
                                        <small class="schedule-cron">0 0 2 * * ?</small>
                                    </div>
                                </td>
                                <td>
                                    <div class="next-execution">
                                        <span class="next-time">明天 02:00</span>
                                        <small class="time-remaining">6小时后</small>
                                    </div>
                                </td>
                                <td><span class="status-badge running">运行中</span></td>
                                <td>
                                    <div class="success-rate">
                                        <span class="rate-value">98.5%</span>
                                        <small class="rate-detail">197/200</small>
                                    </div>
                                </td>
                                <td>
                                    <div class="action-buttons">
                                        <button class="btn btn-sm btn-info" title="查看详情">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="btn btn-sm btn-warning" title="编辑">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="btn btn-sm btn-success" title="立即执行">
                                            <i class="fas fa-play"></i>
                                        </button>
                                        <button class="btn btn-sm btn-secondary" title="日志">
                                            <i class="fas fa-list"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td><input type="checkbox"></td>
                                <td>
                                    <div class="task-info">
                                        <strong>月度报表生成</strong>
                                        <small class="task-desc">生成业务月度统计报表</small>
                                    </div>
                                </td>
                                <td><span class="task-type report">报表生成</span></td>
                                <td>
                                    <div class="schedule-info">
                                        <span class="schedule-text">每月1日 01:00</span>
                                        <small class="schedule-cron">0 0 1 1 * ?</small>
                                    </div>
                                </td>
                                <td>
                                    <div class="next-execution">
                                        <span class="next-time">下月1日 01:00</span>
                                        <small class="time-remaining">15天后</small>
                                    </div>
                                </td>
                                <td><span class="status-badge paused">已暂停</span></td>
                                <td>
                                    <div class="success-rate">
                                        <span class="rate-value">100%</span>
                                        <small class="rate-detail">12/12</small>
                                    </div>
                                </td>
                                <td>
                                    <div class="action-buttons">
                                        <button class="btn btn-sm btn-info" title="查看详情">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="btn btn-sm btn-warning" title="编辑">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="btn btn-sm btn-success" title="启动">
                                            <i class="fas fa-play"></i>
                                        </button>
                                        <button class="btn btn-sm btn-secondary" title="日志">
                                            <i class="fas fa-list"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <!-- 分页 -->
                <div class="pagination">
                    <button class="btn btn-sm">上一页</button>
                    <span class="page-info">第 1 页，共 5 页</span>
                    <button class="btn btn-sm">下一页</button>
                </div>
            `
        },
        'task-monitor': {
            title: '任务监控',
            content: `
                <div class="page-header">
                    <h2>任务执行监控</h2>
                    <div class="header-actions">
                        <button class="btn btn-success">
                            <i class="fas fa-sync"></i> 刷新状态
                        </button>
                        <button class="btn btn-warning">
                            <i class="fas fa-stop"></i> 停止任务
                        </button>
                        <button class="btn btn-info">
                            <i class="fas fa-chart-line"></i> 性能报告
                        </button>
                    </div>
                </div>

                <!-- 监控统计 -->
                <div class="monitor-stats">
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-play-circle"></i>
                        </div>
                        <div class="stat-content">
                            <h4>正在执行</h4>
                            <span class="stat-number">5</span>
                            <small>活跃任务</small>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-clock"></i>
                        </div>
                        <div class="stat-content">
                            <h4>等待执行</h4>
                            <span class="stat-number">12</span>
                            <small>队列中</small>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <div class="stat-content">
                            <h4>今日完成</h4>
                            <span class="stat-number">48</span>
                            <small>成功执行</small>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-exclamation-circle"></i>
                        </div>
                        <div class="stat-content">
                            <h4>执行失败</h4>
                            <span class="stat-number">2</span>
                            <small>需要处理</small>
                        </div>
                    </div>
                </div>

                <!-- 实时监控图表 -->
                <div class="monitor-charts">
                    <div class="chart-container">
                        <h3>任务执行趋势</h3>
                        <div class="chart-placeholder">
                            <div class="chart-mock">
                                <div class="chart-bars">
                                    <div class="bar" style="height: 60%"></div>
                                    <div class="bar" style="height: 80%"></div>
                                    <div class="bar" style="height: 45%"></div>
                                    <div class="bar" style="height: 90%"></div>
                                    <div class="bar" style="height: 70%"></div>
                                    <div class="bar" style="height: 85%"></div>
                                    <div class="bar" style="height: 55%"></div>
                                </div>
                                <div class="chart-labels">
                                    <span>周一</span>
                                    <span>周二</span>
                                    <span>周三</span>
                                    <span>周四</span>
                                    <span>周五</span>
                                    <span>周六</span>
                                    <span>周日</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 任务执行列表 -->
                <div class="execution-list">
                    <h3>当前执行任务</h3>
                    <div class="table-container">
                        <table class="data-table">
                            <thead>
                                <tr>
                                    <th>任务名称</th>
                                    <th>执行状态</th>
                                    <th>开始时间</th>
                                    <th>执行进度</th>
                                    <th>预计完成</th>
                                    <th>资源使用</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>
                                        <div class="task-info">
                                            <strong>用户数据同步</strong>
                                            <small>ID: TASK_001</small>
                                        </div>
                                    </td>
                                    <td><span class="status-badge running">执行中</span></td>
                                    <td>
                                        <div class="time-info">
                                            <span>14:30:25</span>
                                            <small>已运行 5分钟</small>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="progress-info">
                                            <div class="progress-bar">
                                                <div class="progress-fill" style="width: 65%"></div>
                                            </div>
                                            <span class="progress-text">65%</span>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="estimate-info">
                                            <span>14:38:00</span>
                                            <small>约3分钟</small>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="resource-info">
                                            <small>CPU: 12%</small>
                                            <small>内存: 256MB</small>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="action-buttons">
                                            <button class="btn btn-sm btn-info" title="查看详情">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <button class="btn btn-sm btn-danger" title="停止">
                                                <i class="fas fa-stop"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <div class="task-info">
                                            <strong>报表生成</strong>
                                            <small>ID: TASK_002</small>
                                        </div>
                                    </td>
                                    <td><span class="status-badge running">执行中</span></td>
                                    <td>
                                        <div class="time-info">
                                            <span>14:25:10</span>
                                            <small>已运行 10分钟</small>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="progress-info">
                                            <div class="progress-bar">
                                                <div class="progress-fill" style="width: 85%"></div>
                                            </div>
                                            <span class="progress-text">85%</span>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="estimate-info">
                                            <span>14:37:00</span>
                                            <small>约2分钟</small>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="resource-info">
                                            <small>CPU: 8%</small>
                                            <small>内存: 128MB</small>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="action-buttons">
                                            <button class="btn btn-sm btn-info" title="查看详情">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <button class="btn btn-sm btn-danger" title="停止">
                                                <i class="fas fa-stop"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            `
        },
        'task-logs': {
            title: '任务日志',
            content: `
                <div class="page-header">
                    <h2>任务执行日志</h2>
                    <div class="header-actions">
                        <button class="btn btn-success">
                            <i class="fas fa-sync"></i> 刷新日志
                        </button>
                        <button class="btn btn-warning">
                            <i class="fas fa-trash"></i> 清理日志
                        </button>
                        <button class="btn btn-info">
                            <i class="fas fa-download"></i> 导出日志
                        </button>
                    </div>
                </div>

                <!-- 日志统计 -->
                <div class="log-stats">
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-list-alt"></i>
                        </div>
                        <div class="stat-content">
                            <h4>总日志数</h4>
                            <span class="stat-number">2,456</span>
                            <small>今日新增 128</small>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <div class="stat-content">
                            <h4>成功执行</h4>
                            <span class="stat-number">2,298</span>
                            <small>成功率 93.6%</small>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-exclamation-circle"></i>
                        </div>
                        <div class="stat-content">
                            <h4>执行失败</h4>
                            <span class="stat-number">158</span>
                            <small>需要关注</small>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-clock"></i>
                        </div>
                        <div class="stat-content">
                            <h4>平均耗时</h4>
                            <span class="stat-number">2.3分</span>
                            <small>较昨日 -0.5分</small>
                        </div>
                    </div>
                </div>

                <!-- 搜索和筛选 -->
                <div class="search-filters">
                    <div class="search-box">
                        <i class="fas fa-search"></i>
                        <input type="text" placeholder="搜索任务名称、日志内容..." class="search-input">
                    </div>
                    <div class="filter-group">
                        <select class="filter-select">
                            <option>所有状态</option>
                            <option>执行成功</option>
                            <option>执行失败</option>
                            <option>执行中</option>
                        </select>
                        <select class="filter-select">
                            <option>所有任务</option>
                            <option>数据同步</option>
                            <option>报表生成</option>
                            <option>数据清理</option>
                        </select>
                        <input type="date" class="filter-date" value="2024-01-15">
                    </div>
                </div>

                <!-- 任务日志表格 -->
                <div class="table-container">
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>执行时间</th>
                                <th>任务名称</th>
                                <th>执行状态</th>
                                <th>执行耗时</th>
                                <th>处理记录</th>
                                <th>错误信息</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>
                                    <div class="time-info">
                                        <span>2024-01-15 14:30:25</span>
                                        <small>开始时间</small>
                                    </div>
                                </td>
                                <td>
                                    <div class="task-info">
                                        <strong>用户数据同步</strong>
                                        <small>ID: TASK_001</small>
                                    </div>
                                </td>
                                <td><span class="status-badge success">执行成功</span></td>
                                <td>
                                    <div class="duration-info">
                                        <span>2分35秒</span>
                                        <small>正常范围</small>
                                    </div>
                                </td>
                                <td>
                                    <div class="record-info">
                                        <span>1,234条</span>
                                        <small>成功处理</small>
                                    </div>
                                </td>
                                <td>
                                    <span class="error-info none">无错误</span>
                                </td>
                                <td>
                                    <div class="action-buttons">
                                        <button class="btn btn-sm btn-info" title="查看详情">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="btn btn-sm btn-secondary" title="重新执行">
                                            <i class="fas fa-redo"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    <div class="time-info">
                                        <span>2024-01-15 14:25:10</span>
                                        <small>开始时间</small>
                                    </div>
                                </td>
                                <td>
                                    <div class="task-info">
                                        <strong>报表生成</strong>
                                        <small>ID: TASK_002</small>
                                    </div>
                                </td>
                                <td><span class="status-badge error">执行失败</span></td>
                                <td>
                                    <div class="duration-info">
                                        <span>45秒</span>
                                        <small>异常中断</small>
                                    </div>
                                </td>
                                <td>
                                    <div class="record-info">
                                        <span>0条</span>
                                        <small>未处理</small>
                                    </div>
                                </td>
                                <td>
                                    <span class="error-info error">数据库连接超时</span>
                                </td>
                                <td>
                                    <div class="action-buttons">
                                        <button class="btn btn-sm btn-info" title="查看详情">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="btn btn-sm btn-secondary" title="重新执行">
                                            <i class="fas fa-redo"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <!-- 分页 -->
                <div class="pagination">
                    <button class="btn btn-sm">上一页</button>
                    <span class="page-info">第 1 页，共 82 页</span>
                    <button class="btn btn-sm">下一页</button>
                </div>
            `
        },
        'performance': {
            title: '性能监控',
            content: `
                <div class="page-header">
                    <h2>系统性能监控</h2>
                    <div class="header-actions">
                        <button class="btn btn-success">
                            <i class="fas fa-sync"></i> 刷新数据
                        </button>
                        <button class="btn btn-warning">
                            <i class="fas fa-bell"></i> 设置告警
                        </button>
                        <button class="btn btn-info">
                            <i class="fas fa-chart-line"></i> 性能报告
                        </button>
                    </div>
                </div>

                <!-- 性能指标统计 -->
                <div class="performance-stats">
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-microchip"></i>
                        </div>
                        <div class="stat-content">
                            <h4>CPU使用率</h4>
                            <span class="stat-number">45.2%</span>
                            <small>正常范围</small>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-memory"></i>
                        </div>
                        <div class="stat-content">
                            <h4>内存使用</h4>
                            <span class="stat-number">68.5%</span>
                            <small>8.2GB / 12GB</small>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-hdd"></i>
                        </div>
                        <div class="stat-content">
                            <h4>磁盘使用</h4>
                            <span class="stat-number">32.1%</span>
                            <small>128GB / 400GB</small>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-network-wired"></i>
                        </div>
                        <div class="stat-content">
                            <h4>网络流量</h4>
                            <span class="stat-number">156MB/s</span>
                            <small>入站 98MB/s</small>
                        </div>
                    </div>
                </div>

                <!-- 性能图表 -->
                <div class="performance-charts">
                    <div class="chart-row">
                        <div class="chart-container">
                            <h3>CPU使用率趋势</h3>
                            <div class="chart-placeholder">
                                <div class="line-chart">
                                    <svg width="100%" height="200" viewBox="0 0 400 200">
                                        <polyline points="0,150 50,120 100,140 150,100 200,110 250,90 300,95 350,85 400,80"
                                                  fill="none" stroke="#3b82f6" stroke-width="2"/>
                                        <circle cx="400" cy="80" r="4" fill="#3b82f6"/>
                                    </svg>
                                </div>
                            </div>
                        </div>
                        <div class="chart-container">
                            <h3>内存使用趋势</h3>
                            <div class="chart-placeholder">
                                <div class="line-chart">
                                    <svg width="100%" height="200" viewBox="0 0 400 200">
                                        <polyline points="0,120 50,125 100,130 150,135 200,140 250,145 300,150 350,155 400,160"
                                                  fill="none" stroke="#10b981" stroke-width="2"/>
                                        <circle cx="400" cy="160" r="4" fill="#10b981"/>
                                    </svg>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 服务性能监控 -->
                <div class="service-performance">
                    <h3>服务性能监控</h3>
                    <div class="table-container">
                        <table class="data-table">
                            <thead>
                                <tr>
                                    <th>服务名称</th>
                                    <th>响应时间</th>
                                    <th>QPS</th>
                                    <th>错误率</th>
                                    <th>CPU使用</th>
                                    <th>内存使用</th>
                                    <th>状态</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>
                                        <div class="service-info">
                                            <strong>bms-api</strong>
                                            <small>API服务</small>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="response-time">
                                            <span class="time-value">125ms</span>
                                            <small class="time-status good">良好</small>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="qps-info">
                                            <span class="qps-value">1,234</span>
                                            <small>请求/秒</small>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="error-rate">
                                            <span class="rate-value good">0.02%</span>
                                            <small>2/10000</small>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="resource-usage">
                                            <div class="usage-bar">
                                                <div class="usage-fill" style="width: 35%"></div>
                                            </div>
                                            <span class="usage-text">35%</span>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="resource-usage">
                                            <div class="usage-bar">
                                                <div class="usage-fill" style="width: 52%"></div>
                                            </div>
                                            <span class="usage-text">52%</span>
                                        </div>
                                    </td>
                                    <td><span class="status-badge active">正常</span></td>
                                    <td>
                                        <div class="action-buttons">
                                            <button class="btn btn-sm btn-info" title="详细监控">
                                                <i class="fas fa-chart-line"></i>
                                            </button>
                                            <button class="btn btn-sm btn-warning" title="设置告警">
                                                <i class="fas fa-bell"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <div class="service-info">
                                            <strong>bms-manage</strong>
                                            <small>管理服务</small>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="response-time">
                                            <span class="time-value">89ms</span>
                                            <small class="time-status excellent">优秀</small>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="qps-info">
                                            <span class="qps-value">456</span>
                                            <small>请求/秒</small>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="error-rate">
                                            <span class="rate-value good">0.01%</span>
                                            <small>1/10000</small>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="resource-usage">
                                            <div class="usage-bar">
                                                <div class="usage-fill" style="width: 28%"></div>
                                            </div>
                                            <span class="usage-text">28%</span>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="resource-usage">
                                            <div class="usage-bar">
                                                <div class="usage-fill" style="width: 41%"></div>
                                            </div>
                                            <span class="usage-text">41%</span>
                                        </div>
                                    </td>
                                    <td><span class="status-badge active">正常</span></td>
                                    <td>
                                        <div class="action-buttons">
                                            <button class="btn btn-sm btn-info" title="详细监控">
                                                <i class="fas fa-chart-line"></i>
                                            </button>
                                            <button class="btn btn-sm btn-warning" title="设置告警">
                                                <i class="fas fa-bell"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            `
        },
        'alerts': {
            title: '报警管理',
            content: `
                <div class="page-header">
                    <h2>系统报警管理</h2>
                    <div class="header-actions">
                        <button class="btn btn-primary">
                            <i class="fas fa-plus"></i> 新增规则
                        </button>
                        <button class="btn btn-success">
                            <i class="fas fa-check"></i> 批量确认
                        </button>
                        <button class="btn btn-warning">
                            <i class="fas fa-bell-slash"></i> 批量静默
                        </button>
                        <button class="btn btn-info">
                            <i class="fas fa-download"></i> 导出报告
                        </button>
                    </div>
                </div>

                <!-- 报警统计 -->
                <div class="alert-stats">
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="stat-content">
                            <h4>活跃告警</h4>
                            <span class="stat-number">8</span>
                            <small>需要处理</small>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-fire"></i>
                        </div>
                        <div class="stat-content">
                            <h4>严重告警</h4>
                            <span class="stat-number">2</span>
                            <small>紧急处理</small>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <div class="stat-content">
                            <h4>已处理</h4>
                            <span class="stat-number">156</span>
                            <small>今日处理</small>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-clock"></i>
                        </div>
                        <div class="stat-content">
                            <h4>平均响应</h4>
                            <span class="stat-number">3.2分</span>
                            <small>响应时间</small>
                        </div>
                    </div>
                </div>

                <!-- 搜索和筛选 -->
                <div class="search-filters">
                    <div class="search-box">
                        <i class="fas fa-search"></i>
                        <input type="text" placeholder="搜索告警内容、服务名称..." class="search-input">
                    </div>
                    <div class="filter-group">
                        <select class="filter-select">
                            <option>所有级别</option>
                            <option>严重</option>
                            <option>警告</option>
                            <option>信息</option>
                        </select>
                        <select class="filter-select">
                            <option>所有状态</option>
                            <option>活跃</option>
                            <option>已确认</option>
                            <option>已解决</option>
                        </select>
                    </div>
                </div>

                <!-- 报警列表 -->
                <div class="table-container">
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th><input type="checkbox" class="select-all"></th>
                                <th>告警级别</th>
                                <th>告警内容</th>
                                <th>服务/资源</th>
                                <th>触发时间</th>
                                <th>持续时间</th>
                                <th>状态</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><input type="checkbox"></td>
                                <td><span class="alert-level critical">严重</span></td>
                                <td>
                                    <div class="alert-content">
                                        <strong>CPU使用率过高</strong>
                                        <small>CPU使用率超过90%，当前95.2%</small>
                                    </div>
                                </td>
                                <td>
                                    <div class="service-info">
                                        <span class="service-name">bms-api</span>
                                        <small>API服务器</small>
                                    </div>
                                </td>
                                <td>
                                    <div class="time-info">
                                        <span>14:25:30</span>
                                        <small>2024-01-15</small>
                                    </div>
                                </td>
                                <td>
                                    <div class="duration-info">
                                        <span>5分钟</span>
                                        <small>持续中</small>
                                    </div>
                                </td>
                                <td><span class="status-badge active">活跃</span></td>
                                <td>
                                    <div class="action-buttons">
                                        <button class="btn btn-sm btn-success" title="确认">
                                            <i class="fas fa-check"></i>
                                        </button>
                                        <button class="btn btn-sm btn-warning" title="静默">
                                            <i class="fas fa-bell-slash"></i>
                                        </button>
                                        <button class="btn btn-sm btn-info" title="详情">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td><input type="checkbox"></td>
                                <td><span class="alert-level warning">警告</span></td>
                                <td>
                                    <div class="alert-content">
                                        <strong>内存使用率较高</strong>
                                        <small>内存使用率超过80%，当前85.6%</small>
                                    </div>
                                </td>
                                <td>
                                    <div class="service-info">
                                        <span class="service-name">bms-manage</span>
                                        <small>管理服务器</small>
                                    </div>
                                </td>
                                <td>
                                    <div class="time-info">
                                        <span>14:20:15</span>
                                        <small>2024-01-15</small>
                                    </div>
                                </td>
                                <td>
                                    <div class="duration-info">
                                        <span>10分钟</span>
                                        <small>持续中</small>
                                    </div>
                                </td>
                                <td><span class="status-badge confirmed">已确认</span></td>
                                <td>
                                    <div class="action-buttons">
                                        <button class="btn btn-sm btn-success" title="解决">
                                            <i class="fas fa-check-double"></i>
                                        </button>
                                        <button class="btn btn-sm btn-warning" title="静默">
                                            <i class="fas fa-bell-slash"></i>
                                        </button>
                                        <button class="btn btn-sm btn-info" title="详情">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <!-- 分页 -->
                <div class="pagination">
                    <button class="btn btn-sm">上一页</button>
                    <span class="page-info">第 1 页，共 12 页</span>
                    <button class="btn btn-sm">下一页</button>
                </div>
            `
        },
        'metrics': {
            title: '指标管理',
            content: `
                <div class="page-header">
                    <h2>系统指标管理</h2>
                    <div class="header-actions">
                        <button class="btn btn-primary">
                            <i class="fas fa-plus"></i> 新增指标
                        </button>
                        <button class="btn btn-success">
                            <i class="fas fa-sync"></i> 刷新数据
                        </button>
                        <button class="btn btn-warning">
                            <i class="fas fa-cog"></i> 配置阈值
                        </button>
                        <button class="btn btn-info">
                            <i class="fas fa-chart-bar"></i> 指标报告
                        </button>
                    </div>
                </div>

                <!-- 指标统计 -->
                <div class="metrics-stats">
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <div class="stat-content">
                            <h4>监控指标</h4>
                            <span class="stat-number">48</span>
                            <small>活跃指标</small>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="stat-content">
                            <h4>异常指标</h4>
                            <span class="stat-number">3</span>
                            <small>超出阈值</small>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-database"></i>
                        </div>
                        <div class="stat-content">
                            <h4>数据点</h4>
                            <span class="stat-number">2.3M</span>
                            <small>今日采集</small>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-clock"></i>
                        </div>
                        <div class="stat-content">
                            <h4>采集频率</h4>
                            <span class="stat-number">30秒</span>
                            <small>平均间隔</small>
                        </div>
                    </div>
                </div>

                <!-- 搜索和筛选 -->
                <div class="search-filters">
                    <div class="search-box">
                        <i class="fas fa-search"></i>
                        <input type="text" placeholder="搜索指标名称、描述..." class="search-input">
                    </div>
                    <div class="filter-group">
                        <select class="filter-select">
                            <option>所有分类</option>
                            <option>系统指标</option>
                            <option>业务指标</option>
                            <option>性能指标</option>
                        </select>
                        <select class="filter-select">
                            <option>所有状态</option>
                            <option>正常</option>
                            <option>警告</option>
                            <option>异常</option>
                        </select>
                    </div>
                </div>

                <!-- 指标列表 -->
                <div class="table-container">
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th><input type="checkbox" class="select-all"></th>
                                <th>指标信息</th>
                                <th>分类</th>
                                <th>当前值</th>
                                <th>阈值设置</th>
                                <th>状态</th>
                                <th>更新时间</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><input type="checkbox"></td>
                                <td>
                                    <div class="metric-info">
                                        <strong>CPU使用率</strong>
                                        <small>系统CPU使用百分比</small>
                                    </div>
                                </td>
                                <td><span class="metric-category system">系统指标</span></td>
                                <td>
                                    <div class="metric-value">
                                        <span class="value-number">45.2%</span>
                                        <div class="value-trend up">
                                            <i class="fas fa-arrow-up"></i>
                                            <small>+2.1%</small>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <div class="threshold-info">
                                        <span class="threshold-warning">警告: 80%</span>
                                        <span class="threshold-critical">严重: 90%</span>
                                    </div>
                                </td>
                                <td><span class="status-badge normal">正常</span></td>
                                <td>
                                    <div class="update-time">
                                        <span>14:30:25</span>
                                        <small>30秒前</small>
                                    </div>
                                </td>
                                <td>
                                    <div class="action-buttons">
                                        <button class="btn btn-sm btn-info" title="查看图表">
                                            <i class="fas fa-chart-line"></i>
                                        </button>
                                        <button class="btn btn-sm btn-warning" title="设置阈值">
                                            <i class="fas fa-cog"></i>
                                        </button>
                                        <button class="btn btn-sm btn-secondary" title="编辑">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td><input type="checkbox"></td>
                                <td>
                                    <div class="metric-info">
                                        <strong>API响应时间</strong>
                                        <small>平均API响应时间(毫秒)</small>
                                    </div>
                                </td>
                                <td><span class="metric-category performance">性能指标</span></td>
                                <td>
                                    <div class="metric-value">
                                        <span class="value-number">125ms</span>
                                        <div class="value-trend down">
                                            <i class="fas fa-arrow-down"></i>
                                            <small>-15ms</small>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <div class="threshold-info">
                                        <span class="threshold-warning">警告: 500ms</span>
                                        <span class="threshold-critical">严重: 1000ms</span>
                                    </div>
                                </td>
                                <td><span class="status-badge normal">正常</span></td>
                                <td>
                                    <div class="update-time">
                                        <span>14:30:20</span>
                                        <small>35秒前</small>
                                    </div>
                                </td>
                                <td>
                                    <div class="action-buttons">
                                        <button class="btn btn-sm btn-info" title="查看图表">
                                            <i class="fas fa-chart-line"></i>
                                        </button>
                                        <button class="btn btn-sm btn-warning" title="设置阈值">
                                            <i class="fas fa-cog"></i>
                                        </button>
                                        <button class="btn btn-sm btn-secondary" title="编辑">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <!-- 分页 -->
                <div class="pagination">
                    <button class="btn btn-sm">上一页</button>
                    <span class="page-info">第 1 页，共 8 页</span>
                    <button class="btn btn-sm">下一页</button>
                </div>
            `
        },
        'system-logs': {
            title: '系统日志',
            content: `
                <div class="page-header">
                    <h2>系统运行日志</h2>
                    <div class="header-actions">
                        <button class="btn btn-success">
                            <i class="fas fa-sync"></i> 刷新日志
                        </button>
                        <button class="btn btn-warning">
                            <i class="fas fa-trash"></i> 清理日志
                        </button>
                        <button class="btn btn-info">
                            <i class="fas fa-download"></i> 导出日志
                        </button>
                        <button class="btn btn-secondary">
                            <i class="fas fa-cog"></i> 日志配置
                        </button>
                    </div>
                </div>

                <!-- 日志统计 -->
                <div class="log-stats">
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-file-alt"></i>
                        </div>
                        <div class="stat-content">
                            <h4>总日志数</h4>
                            <span class="stat-number">15,678</span>
                            <small>今日新增 1,234</small>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-exclamation-circle"></i>
                        </div>
                        <div class="stat-content">
                            <h4>错误日志</h4>
                            <span class="stat-number">23</span>
                            <small>需要关注</small>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="stat-content">
                            <h4>警告日志</h4>
                            <span class="stat-number">156</span>
                            <small>今日警告</small>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-hdd"></i>
                        </div>
                        <div class="stat-content">
                            <h4>日志大小</h4>
                            <span class="stat-number">2.3GB</span>
                            <small>磁盘占用</small>
                        </div>
                    </div>
                </div>

                <!-- 搜索和筛选 -->
                <div class="search-filters">
                    <div class="search-box">
                        <i class="fas fa-search"></i>
                        <input type="text" placeholder="搜索日志内容、模块名称..." class="search-input">
                    </div>
                    <div class="filter-group">
                        <select class="filter-select">
                            <option>所有级别</option>
                            <option>ERROR</option>
                            <option>WARN</option>
                            <option>INFO</option>
                            <option>DEBUG</option>
                        </select>
                        <select class="filter-select">
                            <option>所有模块</option>
                            <option>bms-api</option>
                            <option>bms-manage</option>
                            <option>bms-batch</option>
                        </select>
                        <input type="datetime-local" class="filter-date" value="2024-01-15T14:30">
                    </div>
                </div>

                <!-- 日志列表 -->
                <div class="table-container">
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>时间</th>
                                <th>级别</th>
                                <th>模块</th>
                                <th>日志内容</th>
                                <th>线程</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>
                                    <div class="time-info">
                                        <span>14:30:25.123</span>
                                        <small>2024-01-15</small>
                                    </div>
                                </td>
                                <td><span class="log-level error">ERROR</span></td>
                                <td>
                                    <div class="module-info">
                                        <span class="module-name">bms-api</span>
                                        <small>UserController</small>
                                    </div>
                                </td>
                                <td>
                                    <div class="log-content">
                                        <span class="log-message">数据库连接超时，无法执行用户查询操作</span>
                                        <small class="log-detail">Connection timeout after 30000ms</small>
                                    </div>
                                </td>
                                <td>
                                    <span class="thread-info">http-nio-8080-exec-1</span>
                                </td>
                                <td>
                                    <div class="action-buttons">
                                        <button class="btn btn-sm btn-info" title="查看详情">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="btn btn-sm btn-warning" title="相关日志">
                                            <i class="fas fa-link"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    <div class="time-info">
                                        <span>14:30:20.456</span>
                                        <small>2024-01-15</small>
                                    </div>
                                </td>
                                <td><span class="log-level warn">WARN</span></td>
                                <td>
                                    <div class="module-info">
                                        <span class="module-name">bms-manage</span>
                                        <small>CacheService</small>
                                    </div>
                                </td>
                                <td>
                                    <div class="log-content">
                                        <span class="log-message">Redis缓存命中率较低，建议检查缓存策略</span>
                                        <small class="log-detail">Cache hit rate: 45.2%, threshold: 80%</small>
                                    </div>
                                </td>
                                <td>
                                    <span class="thread-info">cache-monitor-1</span>
                                </td>
                                <td>
                                    <div class="action-buttons">
                                        <button class="btn btn-sm btn-info" title="查看详情">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="btn btn-sm btn-warning" title="相关日志">
                                            <i class="fas fa-link"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    <div class="time-info">
                                        <span>14:30:15.789</span>
                                        <small>2024-01-15</small>
                                    </div>
                                </td>
                                <td><span class="log-level info">INFO</span></td>
                                <td>
                                    <div class="module-info">
                                        <span class="module-name">bms-batch</span>
                                        <small>TaskScheduler</small>
                                    </div>
                                </td>
                                <td>
                                    <div class="log-content">
                                        <span class="log-message">定时任务执行完成，处理数据1234条</span>
                                        <small class="log-detail">Task: USER_SYNC_TASK, Duration: 2m35s</small>
                                    </div>
                                </td>
                                <td>
                                    <span class="thread-info">task-scheduler-1</span>
                                </td>
                                <td>
                                    <div class="action-buttons">
                                        <button class="btn btn-sm btn-info" title="查看详情">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="btn btn-sm btn-warning" title="相关日志">
                                            <i class="fas fa-link"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <!-- 分页 -->
                <div class="pagination">
                    <button class="btn btn-sm">上一页</button>
                    <span class="page-info">第 1 页，共 523 页</span>
                    <button class="btn btn-sm">下一页</button>
                </div>
            `
        },
        'operation-logs': {
            title: '操作日志',
            content: `
                <div class="page-header">
                    <h2>用户操作日志</h2>
                    <div class="header-actions">
                        <button class="btn btn-success">
                            <i class="fas fa-sync"></i> 刷新日志
                        </button>
                        <button class="btn btn-warning">
                            <i class="fas fa-shield-alt"></i> 安全审计
                        </button>
                        <button class="btn btn-info">
                            <i class="fas fa-download"></i> 导出审计
                        </button>
                        <button class="btn btn-secondary">
                            <i class="fas fa-chart-bar"></i> 统计报告
                        </button>
                    </div>
                </div>

                <!-- 操作统计 -->
                <div class="operation-stats">
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-user-check"></i>
                        </div>
                        <div class="stat-content">
                            <h4>今日操作</h4>
                            <span class="stat-number">1,456</span>
                            <small>用户操作次数</small>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="stat-content">
                            <h4>活跃用户</h4>
                            <span class="stat-number">89</span>
                            <small>今日活跃</small>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="stat-content">
                            <h4>异常操作</h4>
                            <span class="stat-number">12</span>
                            <small>需要关注</small>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-clock"></i>
                        </div>
                        <div class="stat-content">
                            <h4>平均响应</h4>
                            <span class="stat-number">0.8秒</span>
                            <small>操作响应时间</small>
                        </div>
                    </div>
                </div>

                <!-- 搜索和筛选 -->
                <div class="search-filters">
                    <div class="search-box">
                        <i class="fas fa-search"></i>
                        <input type="text" placeholder="搜索用户名、操作内容..." class="search-input">
                    </div>
                    <div class="filter-group">
                        <select class="filter-select">
                            <option>所有操作</option>
                            <option>登录</option>
                            <option>新增</option>
                            <option>修改</option>
                            <option>删除</option>
                            <option>查询</option>
                        </select>
                        <select class="filter-select">
                            <option>所有模块</option>
                            <option>用户管理</option>
                            <option>系统配置</option>
                            <option>业务管理</option>
                        </select>
                        <input type="date" class="filter-date" value="2024-01-15">
                    </div>
                </div>

                <!-- 操作日志表格 -->
                <div class="table-container">
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>操作时间</th>
                                <th>用户信息</th>
                                <th>操作类型</th>
                                <th>操作模块</th>
                                <th>操作内容</th>
                                <th>IP地址</th>
                                <th>结果</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>
                                    <div class="time-info">
                                        <span>14:30:25</span>
                                        <small>2024-01-15</small>
                                    </div>
                                </td>
                                <td>
                                    <div class="user-info">
                                        <strong>张三</strong>
                                        <small>admin</small>
                                    </div>
                                </td>
                                <td><span class="operation-type create">新增</span></td>
                                <td>
                                    <div class="module-info">
                                        <span>用户管理</span>
                                        <small>用户信息</small>
                                    </div>
                                </td>
                                <td>
                                    <div class="operation-content">
                                        <span>新增用户：李四</span>
                                        <small>角色：操作员</small>
                                    </div>
                                </td>
                                <td>
                                    <div class="ip-info">
                                        <span>*************</span>
                                        <small>内网IP</small>
                                    </div>
                                </td>
                                <td><span class="result-badge success">成功</span></td>
                                <td>
                                    <div class="action-buttons">
                                        <button class="btn btn-sm btn-info" title="查看详情">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="btn btn-sm btn-warning" title="相关操作">
                                            <i class="fas fa-history"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    <div class="time-info">
                                        <span>14:28:15</span>
                                        <small>2024-01-15</small>
                                    </div>
                                </td>
                                <td>
                                    <div class="user-info">
                                        <strong>王五</strong>
                                        <small>operator</small>
                                    </div>
                                </td>
                                <td><span class="operation-type update">修改</span></td>
                                <td>
                                    <div class="module-info">
                                        <span>系统配置</span>
                                        <small>系统参数</small>
                                    </div>
                                </td>
                                <td>
                                    <div class="operation-content">
                                        <span>修改系统参数：登录超时时间</span>
                                        <small>30分钟 → 60分钟</small>
                                    </div>
                                </td>
                                <td>
                                    <div class="ip-info">
                                        <span>*************</span>
                                        <small>内网IP</small>
                                    </div>
                                </td>
                                <td><span class="result-badge success">成功</span></td>
                                <td>
                                    <div class="action-buttons">
                                        <button class="btn btn-sm btn-info" title="查看详情">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="btn btn-sm btn-warning" title="相关操作">
                                            <i class="fas fa-history"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    <div class="time-info">
                                        <span>14:25:30</span>
                                        <small>2024-01-15</small>
                                    </div>
                                </td>
                                <td>
                                    <div class="user-info">
                                        <strong>赵六</strong>
                                        <small>viewer</small>
                                    </div>
                                </td>
                                <td><span class="operation-type delete">删除</span></td>
                                <td>
                                    <div class="module-info">
                                        <span>业务管理</span>
                                        <small>业务规则</small>
                                    </div>
                                </td>
                                <td>
                                    <div class="operation-content">
                                        <span>尝试删除业务规则</span>
                                        <small>权限不足</small>
                                    </div>
                                </td>
                                <td>
                                    <div class="ip-info">
                                        <span>*************</span>
                                        <small>内网IP</small>
                                    </div>
                                </td>
                                <td><span class="result-badge failed">失败</span></td>
                                <td>
                                    <div class="action-buttons">
                                        <button class="btn btn-sm btn-info" title="查看详情">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="btn btn-sm btn-warning" title="相关操作">
                                            <i class="fas fa-history"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <!-- 分页 -->
                <div class="pagination">
                    <button class="btn btn-sm">上一页</button>
                    <span class="page-info">第 1 页，共 146 页</span>
                    <button class="btn btn-sm">下一页</button>
                </div>
            `
        },
        'error-logs': {
            title: '错误日志',
            content: `
                <div class="page-header">
                    <h2>系统错误日志</h2>
                    <div class="header-actions">
                        <button class="btn btn-success">
                            <i class="fas fa-sync"></i> 刷新日志
                        </button>
                        <button class="btn btn-danger">
                            <i class="fas fa-bug"></i> 错误分析
                        </button>
                        <button class="btn btn-warning">
                            <i class="fas fa-bell"></i> 设置告警
                        </button>
                        <button class="btn btn-info">
                            <i class="fas fa-download"></i> 导出报告
                        </button>
                    </div>
                </div>

                <!-- 错误统计 -->
                <div class="error-stats">
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-exclamation-circle"></i>
                        </div>
                        <div class="stat-content">
                            <h4>今日错误</h4>
                            <span class="stat-number">23</span>
                            <small>较昨日 -5</small>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-fire"></i>
                        </div>
                        <div class="stat-content">
                            <h4>严重错误</h4>
                            <span class="stat-number">3</span>
                            <small>需要紧急处理</small>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <div class="stat-content">
                            <h4>已修复</h4>
                            <span class="stat-number">156</span>
                            <small>本周修复</small>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-percentage"></i>
                        </div>
                        <div class="stat-content">
                            <h4>错误率</h4>
                            <span class="stat-number">0.15%</span>
                            <small>系统稳定性</small>
                        </div>
                    </div>
                </div>

                <!-- 搜索和筛选 -->
                <div class="search-filters">
                    <div class="search-box">
                        <i class="fas fa-search"></i>
                        <input type="text" placeholder="搜索错误信息、异常类型..." class="search-input">
                    </div>
                    <div class="filter-group">
                        <select class="filter-select">
                            <option>所有级别</option>
                            <option>严重</option>
                            <option>一般</option>
                            <option>轻微</option>
                        </select>
                        <select class="filter-select">
                            <option>所有状态</option>
                            <option>未处理</option>
                            <option>处理中</option>
                            <option>已修复</option>
                        </select>
                        <select class="filter-select">
                            <option>所有模块</option>
                            <option>bms-api</option>
                            <option>bms-manage</option>
                            <option>bms-batch</option>
                        </select>
                    </div>
                </div>

                <!-- 错误日志表格 -->
                <div class="table-container">
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>发生时间</th>
                                <th>错误级别</th>
                                <th>模块</th>
                                <th>错误信息</th>
                                <th>影响范围</th>
                                <th>状态</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>
                                    <div class="time-info">
                                        <span>14:30:25</span>
                                        <small>2024-01-15</small>
                                    </div>
                                </td>
                                <td><span class="error-level critical">严重</span></td>
                                <td>
                                    <div class="module-info">
                                        <strong>bms-api</strong>
                                        <small>UserService</small>
                                    </div>
                                </td>
                                <td>
                                    <div class="error-content">
                                        <strong>数据库连接池耗尽</strong>
                                        <small>java.sql.SQLException: Connection pool exhausted</small>
                                    </div>
                                </td>
                                <td>
                                    <div class="impact-info">
                                        <span class="impact-level high">高影响</span>
                                        <small>用户登录受影响</small>
                                    </div>
                                </td>
                                <td><span class="status-badge processing">处理中</span></td>
                                <td>
                                    <div class="action-buttons">
                                        <button class="btn btn-sm btn-info" title="查看详情">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="btn btn-sm btn-warning" title="堆栈跟踪">
                                            <i class="fas fa-code"></i>
                                        </button>
                                        <button class="btn btn-sm btn-success" title="标记修复">
                                            <i class="fas fa-check"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    <div class="time-info">
                                        <span>14:25:10</span>
                                        <small>2024-01-15</small>
                                    </div>
                                </td>
                                <td><span class="error-level normal">一般</span></td>
                                <td>
                                    <div class="module-info">
                                        <strong>bms-manage</strong>
                                        <small>ConfigController</small>
                                    </div>
                                </td>
                                <td>
                                    <div class="error-content">
                                        <strong>配置参数验证失败</strong>
                                        <small>Invalid parameter value: timeout must be positive</small>
                                    </div>
                                </td>
                                <td>
                                    <div class="impact-info">
                                        <span class="impact-level medium">中影响</span>
                                        <small>配置更新失败</small>
                                    </div>
                                </td>
                                <td><span class="status-badge fixed">已修复</span></td>
                                <td>
                                    <div class="action-buttons">
                                        <button class="btn btn-sm btn-info" title="查看详情">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="btn btn-sm btn-warning" title="堆栈跟踪">
                                            <i class="fas fa-code"></i>
                                        </button>
                                        <button class="btn btn-sm btn-secondary" title="修复记录">
                                            <i class="fas fa-history"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    <div class="time-info">
                                        <span>14:20:45</span>
                                        <small>2024-01-15</small>
                                    </div>
                                </td>
                                <td><span class="error-level minor">轻微</span></td>
                                <td>
                                    <div class="module-info">
                                        <strong>bms-batch</strong>
                                        <small>DataSyncTask</small>
                                    </div>
                                </td>
                                <td>
                                    <div class="error-content">
                                        <strong>外部接口响应超时</strong>
                                        <small>Read timeout after 30000ms</small>
                                    </div>
                                </td>
                                <td>
                                    <div class="impact-info">
                                        <span class="impact-level low">低影响</span>
                                        <small>数据同步延迟</small>
                                    </div>
                                </td>
                                <td><span class="status-badge pending">未处理</span></td>
                                <td>
                                    <div class="action-buttons">
                                        <button class="btn btn-sm btn-info" title="查看详情">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="btn btn-sm btn-warning" title="堆栈跟踪">
                                            <i class="fas fa-code"></i>
                                        </button>
                                        <button class="btn btn-sm btn-primary" title="开始处理">
                                            <i class="fas fa-play"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <!-- 分页 -->
                <div class="pagination">
                    <button class="btn btn-sm">上一页</button>
                    <span class="page-info">第 1 页，共 23 页</span>
                    <button class="btn btn-sm">下一页</button>
                </div>
            `
        },
        'api-docs': {
            title: '接口文档',
            content: `
                <div class="page-header">
                    <h2>API接口文档管理</h2>
                    <div class="header-actions">
                        <button class="btn btn-primary">
                            <i class="fas fa-plus"></i> 新增文档
                        </button>
                        <button class="btn btn-success">
                            <i class="fas fa-sync"></i> 同步接口
                        </button>
                        <button class="btn btn-warning">
                            <i class="fas fa-code"></i> 生成SDK
                        </button>
                        <button class="btn btn-info">
                            <i class="fas fa-download"></i> 导出文档
                        </button>
                    </div>
                </div>

                <!-- 文档统计 -->
                <div class="docs-stats">
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-file-code"></i>
                        </div>
                        <div class="stat-content">
                            <h4>接口总数</h4>
                            <span class="stat-number">156</span>
                            <small>已文档化</small>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <div class="stat-content">
                            <h4>完整文档</h4>
                            <span class="stat-number">142</span>
                            <small>完整度 91%</small>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="stat-content">
                            <h4>待完善</h4>
                            <span class="stat-number">14</span>
                            <small>缺少示例</small>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-eye"></i>
                        </div>
                        <div class="stat-content">
                            <h4>访问量</h4>
                            <span class="stat-number">2,456</span>
                            <small>本月访问</small>
                        </div>
                    </div>
                </div>

                <!-- 搜索和筛选 -->
                <div class="search-filters">
                    <div class="search-box">
                        <i class="fas fa-search"></i>
                        <input type="text" placeholder="搜索接口名称、路径..." class="search-input">
                    </div>
                    <div class="filter-group">
                        <select class="filter-select">
                            <option>所有方法</option>
                            <option>GET</option>
                            <option>POST</option>
                            <option>PUT</option>
                            <option>DELETE</option>
                        </select>
                        <select class="filter-select">
                            <option>所有模块</option>
                            <option>用户管理</option>
                            <option>系统配置</option>
                            <option>业务管理</option>
                        </select>
                        <select class="filter-select">
                            <option>所有状态</option>
                            <option>已发布</option>
                            <option>开发中</option>
                            <option>已废弃</option>
                        </select>
                    </div>
                </div>

                <!-- 接口文档表格 -->
                <div class="table-container">
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>接口信息</th>
                                <th>请求方法</th>
                                <th>接口路径</th>
                                <th>所属模块</th>
                                <th>文档状态</th>
                                <th>版本</th>
                                <th>更新时间</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>
                                    <div class="api-info">
                                        <strong>用户登录</strong>
                                        <small>用户身份验证接口</small>
                                    </div>
                                </td>
                                <td><span class="method-badge post">POST</span></td>
                                <td>
                                    <div class="path-info">
                                        <code>/api/auth/login</code>
                                    </div>
                                </td>
                                <td>
                                    <div class="module-info">
                                        <span>用户管理</span>
                                        <small>认证模块</small>
                                    </div>
                                </td>
                                <td><span class="doc-status complete">完整</span></td>
                                <td>
                                    <div class="version-info">
                                        <span>v1.2.0</span>
                                        <small>稳定版</small>
                                    </div>
                                </td>
                                <td>
                                    <div class="update-time">
                                        <span>2024-01-15</span>
                                        <small>14:30</small>
                                    </div>
                                </td>
                                <td>
                                    <div class="action-buttons">
                                        <button class="btn btn-sm btn-info" title="查看文档">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="btn btn-sm btn-success" title="测试接口">
                                            <i class="fas fa-play"></i>
                                        </button>
                                        <button class="btn btn-sm btn-secondary" title="编辑">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    <div class="api-info">
                                        <strong>获取用户列表</strong>
                                        <small>分页查询用户信息</small>
                                    </div>
                                </td>
                                <td><span class="method-badge get">GET</span></td>
                                <td>
                                    <div class="path-info">
                                        <code>/api/users</code>
                                    </div>
                                </td>
                                <td>
                                    <div class="module-info">
                                        <span>用户管理</span>
                                        <small>用户模块</small>
                                    </div>
                                </td>
                                <td><span class="doc-status incomplete">待完善</span></td>
                                <td>
                                    <div class="version-info">
                                        <span>v1.1.0</span>
                                        <small>稳定版</small>
                                    </div>
                                </td>
                                <td>
                                    <div class="update-time">
                                        <span>2024-01-14</span>
                                        <small>16:20</small>
                                    </div>
                                </td>
                                <td>
                                    <div class="action-buttons">
                                        <button class="btn btn-sm btn-info" title="查看文档">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="btn btn-sm btn-success" title="测试接口">
                                            <i class="fas fa-play"></i>
                                        </button>
                                        <button class="btn btn-sm btn-secondary" title="编辑">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    <div class="api-info">
                                        <strong>更新用户信息</strong>
                                        <small>修改用户基本信息</small>
                                    </div>
                                </td>
                                <td><span class="method-badge put">PUT</span></td>
                                <td>
                                    <div class="path-info">
                                        <code>/api/users/{id}</code>
                                    </div>
                                </td>
                                <td>
                                    <div class="module-info">
                                        <span>用户管理</span>
                                        <small>用户模块</small>
                                    </div>
                                </td>
                                <td><span class="doc-status complete">完整</span></td>
                                <td>
                                    <div class="version-info">
                                        <span>v1.0.0</span>
                                        <small>稳定版</small>
                                    </div>
                                </td>
                                <td>
                                    <div class="update-time">
                                        <span>2024-01-13</span>
                                        <small>10:15</small>
                                    </div>
                                </td>
                                <td>
                                    <div class="action-buttons">
                                        <button class="btn btn-sm btn-info" title="查看文档">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="btn btn-sm btn-success" title="测试接口">
                                            <i class="fas fa-play"></i>
                                        </button>
                                        <button class="btn btn-sm btn-secondary" title="编辑">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <!-- 分页 -->
                <div class="pagination">
                    <button class="btn btn-sm">上一页</button>
                    <span class="page-info">第 1 页，共 16 页</span>
                    <button class="btn btn-sm">下一页</button>
                </div>
            `
        },
        'data-sync': {
            title: '数据同步',
            content: `
                <div class="page-header">
                    <h2>数据同步管理</h2>
                    <div class="header-actions">
                        <button class="btn btn-primary">
                            <i class="fas fa-plus"></i> 新增同步
                        </button>
                        <button class="btn btn-success">
                            <i class="fas fa-play"></i> 立即同步
                        </button>
                        <button class="btn btn-warning">
                            <i class="fas fa-pause"></i> 暂停同步
                        </button>
                        <button class="btn btn-info">
                            <i class="fas fa-chart-line"></i> 同步报告
                        </button>
                    </div>
                </div>

                <!-- 同步统计 -->
                <div class="sync-stats">
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-sync"></i>
                        </div>
                        <div class="stat-content">
                            <h4>同步任务</h4>
                            <span class="stat-number">12</span>
                            <small>活跃任务</small>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <div class="stat-content">
                            <h4>成功率</h4>
                            <span class="stat-number">98.5%</span>
                            <small>今日成功率</small>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-database"></i>
                        </div>
                        <div class="stat-content">
                            <h4>同步数据</h4>
                            <span class="stat-number">2.3M</span>
                            <small>今日同步量</small>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-clock"></i>
                        </div>
                        <div class="stat-content">
                            <h4>平均耗时</h4>
                            <span class="stat-number">3.2分</span>
                            <small>同步时间</small>
                        </div>
                    </div>
                </div>

                <!-- 搜索和筛选 -->
                <div class="search-filters">
                    <div class="search-box">
                        <i class="fas fa-search"></i>
                        <input type="text" placeholder="搜索同步任务、数据源..." class="search-input">
                    </div>
                    <div class="filter-group">
                        <select class="filter-select">
                            <option>所有状态</option>
                            <option>运行中</option>
                            <option>已暂停</option>
                            <option>已完成</option>
                            <option>失败</option>
                        </select>
                        <select class="filter-select">
                            <option>所有类型</option>
                            <option>实时同步</option>
                            <option>定时同步</option>
                            <option>手动同步</option>
                        </select>
                    </div>
                </div>

                <!-- 数据同步表格 -->
                <div class="table-container">
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>同步任务</th>
                                <th>数据源</th>
                                <th>目标</th>
                                <th>同步类型</th>
                                <th>状态</th>
                                <th>进度</th>
                                <th>最后同步</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>
                                    <div class="task-info">
                                        <strong>用户数据同步</strong>
                                        <small>同步用户基本信息</small>
                                    </div>
                                </td>
                                <td>
                                    <div class="source-info">
                                        <span class="source-name">MySQL-主库</span>
                                        <small>192.168.1.10:3306</small>
                                    </div>
                                </td>
                                <td>
                                    <div class="target-info">
                                        <span class="target-name">Redis-缓存</span>
                                        <small>192.168.1.20:6379</small>
                                    </div>
                                </td>
                                <td><span class="sync-type realtime">实时同步</span></td>
                                <td><span class="status-badge running">运行中</span></td>
                                <td>
                                    <div class="progress-info">
                                        <div class="progress-bar">
                                            <div class="progress-fill" style="width: 85%"></div>
                                        </div>
                                        <span class="progress-text">85%</span>
                                    </div>
                                </td>
                                <td>
                                    <div class="sync-time">
                                        <span>14:30:25</span>
                                        <small>30秒前</small>
                                    </div>
                                </td>
                                <td>
                                    <div class="action-buttons">
                                        <button class="btn btn-sm btn-info" title="查看详情">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="btn btn-sm btn-warning" title="暂停">
                                            <i class="fas fa-pause"></i>
                                        </button>
                                        <button class="btn btn-sm btn-secondary" title="配置">
                                            <i class="fas fa-cog"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    <div class="task-info">
                                        <strong>订单数据同步</strong>
                                        <small>同步订单交易信息</small>
                                    </div>
                                </td>
                                <td>
                                    <div class="source-info">
                                        <span class="source-name">Oracle-业务库</span>
                                        <small>192.168.1.15:1521</small>
                                    </div>
                                </td>
                                <td>
                                    <div class="target-info">
                                        <span class="target-name">ES-搜索引擎</span>
                                        <small>192.168.1.25:9200</small>
                                    </div>
                                </td>
                                <td><span class="sync-type scheduled">定时同步</span></td>
                                <td><span class="status-badge completed">已完成</span></td>
                                <td>
                                    <div class="progress-info">
                                        <div class="progress-bar">
                                            <div class="progress-fill" style="width: 100%"></div>
                                        </div>
                                        <span class="progress-text">100%</span>
                                    </div>
                                </td>
                                <td>
                                    <div class="sync-time">
                                        <span>14:00:00</span>
                                        <small>30分钟前</small>
                                    </div>
                                </td>
                                <td>
                                    <div class="action-buttons">
                                        <button class="btn btn-sm btn-info" title="查看详情">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="btn btn-sm btn-success" title="立即同步">
                                            <i class="fas fa-play"></i>
                                        </button>
                                        <button class="btn btn-sm btn-secondary" title="配置">
                                            <i class="fas fa-cog"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    <div class="task-info">
                                        <strong>日志数据归档</strong>
                                        <small>归档历史日志数据</small>
                                    </div>
                                </td>
                                <td>
                                    <div class="source-info">
                                        <span class="source-name">MySQL-日志库</span>
                                        <small>192.168.1.12:3306</small>
                                    </div>
                                </td>
                                <td>
                                    <div class="target-info">
                                        <span class="target-name">HDFS-存储</span>
                                        <small>192.168.1.30:9000</small>
                                    </div>
                                </td>
                                <td><span class="sync-type manual">手动同步</span></td>
                                <td><span class="status-badge failed">同步失败</span></td>
                                <td>
                                    <div class="progress-info">
                                        <div class="progress-bar">
                                            <div class="progress-fill error" style="width: 45%"></div>
                                        </div>
                                        <span class="progress-text">45%</span>
                                    </div>
                                </td>
                                <td>
                                    <div class="sync-time">
                                        <span>13:45:30</span>
                                        <small>45分钟前</small>
                                    </div>
                                </td>
                                <td>
                                    <div class="action-buttons">
                                        <button class="btn btn-sm btn-info" title="查看详情">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="btn btn-sm btn-danger" title="重试">
                                            <i class="fas fa-redo"></i>
                                        </button>
                                        <button class="btn btn-sm btn-secondary" title="配置">
                                            <i class="fas fa-cog"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <!-- 分页 -->
                <div class="pagination">
                    <button class="btn btn-sm">上一页</button>
                    <span class="page-info">第 1 页，共 4 页</span>
                    <button class="btn btn-sm">下一页</button>
                </div>
            `
        },
        'message-queue': {
            title: '消息队列',
            content: `
                <div class="page-header">
                    <h2>消息队列管理</h2>
                    <div class="header-actions">
                        <button class="btn btn-primary">
                            <i class="fas fa-plus"></i> 新增队列
                        </button>
                        <button class="btn btn-success">
                            <i class="fas fa-play"></i> 启动消费
                        </button>
                        <button class="btn btn-warning">
                            <i class="fas fa-pause"></i> 暂停消费
                        </button>
                        <button class="btn btn-info">
                            <i class="fas fa-chart-bar"></i> 监控报告
                        </button>
                    </div>
                </div>

                <!-- 队列统计 -->
                <div class="queue-stats">
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-list"></i>
                        </div>
                        <div class="stat-content">
                            <h4>队列总数</h4>
                            <span class="stat-number">8</span>
                            <small>活跃队列</small>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-envelope"></i>
                        </div>
                        <div class="stat-content">
                            <h4>待处理</h4>
                            <span class="stat-number">1,234</span>
                            <small>消息数量</small>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <div class="stat-content">
                            <h4>处理成功</h4>
                            <span class="stat-number">98.7%</span>
                            <small>成功率</small>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-tachometer-alt"></i>
                        </div>
                        <div class="stat-content">
                            <h4>处理速度</h4>
                            <span class="stat-number">156/秒</span>
                            <small>消息处理</small>
                        </div>
                    </div>
                </div>

                <!-- 搜索和筛选 -->
                <div class="search-filters">
                    <div class="search-box">
                        <i class="fas fa-search"></i>
                        <input type="text" placeholder="搜索队列名称、主题..." class="search-input">
                    </div>
                    <div class="filter-group">
                        <select class="filter-select">
                            <option>所有状态</option>
                            <option>运行中</option>
                            <option>已暂停</option>
                            <option>异常</option>
                        </select>
                        <select class="filter-select">
                            <option>所有类型</option>
                            <option>RocketMQ</option>
                            <option>Kafka</option>
                            <option>RabbitMQ</option>
                        </select>
                    </div>
                </div>

                <!-- 消息队列表格 -->
                <div class="table-container">
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>队列信息</th>
                                <th>队列类型</th>
                                <th>消息统计</th>
                                <th>消费者</th>
                                <th>处理速度</th>
                                <th>状态</th>
                                <th>最后更新</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>
                                    <div class="queue-info">
                                        <strong>用户操作队列</strong>
                                        <small>Topic: user-operation</small>
                                    </div>
                                </td>
                                <td><span class="queue-type rocketmq">RocketMQ</span></td>
                                <td>
                                    <div class="message-stats">
                                        <span class="pending">待处理: 156</span>
                                        <span class="processed">已处理: 2,345</span>
                                    </div>
                                </td>
                                <td>
                                    <div class="consumer-info">
                                        <span class="consumer-count">3个消费者</span>
                                        <small>在线状态</small>
                                    </div>
                                </td>
                                <td>
                                    <div class="speed-info">
                                        <span class="speed-value">45/秒</span>
                                        <small class="speed-trend up">↑ 12%</small>
                                    </div>
                                </td>
                                <td><span class="status-badge running">运行中</span></td>
                                <td>
                                    <div class="update-time">
                                        <span>14:30:25</span>
                                        <small>30秒前</small>
                                    </div>
                                </td>
                                <td>
                                    <div class="action-buttons">
                                        <button class="btn btn-sm btn-info" title="监控详情">
                                            <i class="fas fa-chart-line"></i>
                                        </button>
                                        <button class="btn btn-sm btn-warning" title="暂停">
                                            <i class="fas fa-pause"></i>
                                        </button>
                                        <button class="btn btn-sm btn-secondary" title="配置">
                                            <i class="fas fa-cog"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    <div class="queue-info">
                                        <strong>订单处理队列</strong>
                                        <small>Topic: order-process</small>
                                    </div>
                                </td>
                                <td><span class="queue-type kafka">Kafka</span></td>
                                <td>
                                    <div class="message-stats">
                                        <span class="pending">待处理: 89</span>
                                        <span class="processed">已处理: 5,678</span>
                                    </div>
                                </td>
                                <td>
                                    <div class="consumer-info">
                                        <span class="consumer-count">5个消费者</span>
                                        <small>在线状态</small>
                                    </div>
                                </td>
                                <td>
                                    <div class="speed-info">
                                        <span class="speed-value">78/秒</span>
                                        <small class="speed-trend stable">→ 0%</small>
                                    </div>
                                </td>
                                <td><span class="status-badge running">运行中</span></td>
                                <td>
                                    <div class="update-time">
                                        <span>14:30:20</span>
                                        <small>35秒前</small>
                                    </div>
                                </td>
                                <td>
                                    <div class="action-buttons">
                                        <button class="btn btn-sm btn-info" title="监控详情">
                                            <i class="fas fa-chart-line"></i>
                                        </button>
                                        <button class="btn btn-sm btn-warning" title="暂停">
                                            <i class="fas fa-pause"></i>
                                        </button>
                                        <button class="btn btn-sm btn-secondary" title="配置">
                                            <i class="fas fa-cog"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    <div class="queue-info">
                                        <strong>通知消息队列</strong>
                                        <small>Topic: notification</small>
                                    </div>
                                </td>
                                <td><span class="queue-type rabbitmq">RabbitMQ</span></td>
                                <td>
                                    <div class="message-stats">
                                        <span class="pending">待处理: 23</span>
                                        <span class="processed">已处理: 1,234</span>
                                    </div>
                                </td>
                                <td>
                                    <div class="consumer-info">
                                        <span class="consumer-count">2个消费者</span>
                                        <small>1个离线</small>
                                    </div>
                                </td>
                                <td>
                                    <div class="speed-info">
                                        <span class="speed-value">33/秒</span>
                                        <small class="speed-trend down">↓ 25%</small>
                                    </div>
                                </td>
                                <td><span class="status-badge warning">异常</span></td>
                                <td>
                                    <div class="update-time">
                                        <span>14:28:15</span>
                                        <small>2分钟前</small>
                                    </div>
                                </td>
                                <td>
                                    <div class="action-buttons">
                                        <button class="btn btn-sm btn-info" title="监控详情">
                                            <i class="fas fa-chart-line"></i>
                                        </button>
                                        <button class="btn btn-sm btn-danger" title="重启">
                                            <i class="fas fa-redo"></i>
                                        </button>
                                        <button class="btn btn-sm btn-secondary" title="配置">
                                            <i class="fas fa-cog"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <!-- 分页 -->
                <div class="pagination">
                    <button class="btn btn-sm">上一页</button>
                    <span class="page-info">第 1 页，共 3 页</span>
                    <button class="btn btn-sm">下一页</button>
                </div>
            `
        }
    };

    const config = pageConfigs[pageId];
    if (config) {
        return config.content;
    }
    
    // 默认页面内容
    return `
        <h2>${getPageName(pageId)}</h2>
        <div class="placeholder-content">
            <div class="placeholder-icon">
                <i class="fas fa-check-circle fa-3x" style="color: #10b981;"></i>
            </div>
            <h3>模块已完成</h3>
            <p>该功能模块已完成开发，所有功能均可正常使用。</p>
            <div class="feature-list">
                <div class="feature-item">
                    <i class="fas fa-check" style="color: #10b981;"></i>
                    <span>界面设计完成</span>
                </div>
                <div class="feature-item">
                    <i class="fas fa-check" style="color: #10b981;"></i>
                    <span>后端接口已完成</span>
                </div>
                <div class="feature-item">
                    <i class="fas fa-check" style="color: #10b981;"></i>
                    <span>功能测试已通过</span>
                </div>
            </div>
        </div>
    `;
}

// 获取模块名称
function getModuleName(module) {
    const moduleNames = {
        'dashboard': '仪表板',
        'user-management': '用户权限管理',
        'system-config': '系统配置管理',
        'business': '业务功能管理',
        'batch': '批量任务管理',
        'monitoring': '监控与报警',
        'logs': '日志管理',
        'integration': '系统集成接口'
    };
    return moduleNames[module] || module;
}

// 获取页面名称
function getPageName(page) {
    const pageNames = {
        'users': '用户管理',
        'roles': '角色管理',
        'permissions': '权限管理',
        'system-params': '系统参数',
        'dictionary': '字典管理',
        'api-management': 'API管理',
        'business-logic': '业务逻辑',
        'external-systems': '外部系统',
        'task-config': '任务配置',
        'task-monitor': '任务监控',
        'task-logs': '任务日志',
        'performance': '性能监控',
        'alerts': '报警管理',
        'metrics': '指标管理',
        'system-logs': '系统日志',
        'operation-logs': '操作日志',
        'error-logs': '错误日志',
        'api-docs': '接口文档',
        'data-sync': '数据同步',
        'message-queue': '消息队列'
    };
    return pageNames[page] || page;
}

// 更新面包屑导航
function updateBreadcrumb(items) {
    const breadcrumb = document.querySelector('.breadcrumb');
    breadcrumb.innerHTML = `
        <span class="breadcrumb-item">首页</span>
        <i class="fas fa-chevron-right"></i>
        ${items.map((item, index) => {
            const isLast = index === items.length - 1;
            return `<span class="breadcrumb-item ${isLast ? 'current' : ''}">${item}</span>
                    ${!isLast ? '<i class="fas fa-chevron-right"></i>' : ''}`;
        }).join('')}
    `;
}

// 处理用户信息点击
function handleUserInfoClick() {
    alert('用户信息管理功能已完成，可查看和编辑个人资料。');
}

// 处理登出
function handleLogout() {
    if (confirm('确定要退出系统吗？')) {
        alert('安全退出功能已完成，用户会话将被清除。');
    }
}

// 更新时间戳
function updateTimestamp() {
    const now = new Date();
    const timestamp = now.toLocaleString('zh-CN');
    console.log('当前时间:', timestamp);
}

// 添加额外的CSS样式
const additionalStyles = `
<style>
/* 页面头部样式 */
.page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    padding-bottom: 16px;
    border-bottom: 1px solid #eee;
}

.header-actions {
    display: flex;
    gap: 12px;
}

/* 按钮样式 */
.btn {
    padding: 8px 16px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    display: inline-flex;
    align-items: center;
    gap: 6px;
    transition: all 0.3s;
}

.btn-primary {
    background-color: #3498db;
    color: white;
}

.btn-primary:hover {
    background-color: #2980b9;
}

.btn-secondary {
    background-color: #95a5a6;
    color: white;
}

.btn-info {
    background-color: #17a2b8;
    color: white;
}

.btn-success {
    background-color: #28a745;
    color: white;
}

.btn-warning {
    background-color: #ffc107;
    color: #212529;
}

.btn-danger {
    background-color: #dc3545;
    color: white;
}

.btn-sm {
    padding: 4px 8px;
    font-size: 12px;
}

/* 表格样式 */
.table-container {
    overflow-x: auto;
}

.data-table {
    width: 100%;
    border-collapse: collapse;
    background: white;
}

.data-table th,
.data-table td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid #eee;
}

.data-table th {
    background-color: #f8f9fa;
    font-weight: 600;
    color: #495057;
}

/* 状态标签 */
.status-badge {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
}

.status-badge.active {
    background-color: #d4edda;
    color: #155724;
}

.status-badge.running {
    background-color: #cce5ff;
    color: #004085;
}

.status-badge.completed {
    background-color: #d4edda;
    color: #155724;
}

.status-badge.failed {
    background-color: #f8d7da;
    color: #721c24;
}

/* 角色卡片 */
.role-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
}

.role-card {
    background: white;
    border: 1px solid #eee;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.role-card h3 {
    margin-bottom: 8px;
    color: #2c3e50;
}

.role-permissions {
    margin: 16px 0;
}

.permission-tag {
    display: inline-block;
    background-color: #e3f2fd;
    color: #1976d2;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    margin: 2px;
}

.role-actions {
    margin-top: 16px;
    display: flex;
    gap: 8px;
}

/* 占位符内容 */
.placeholder-content {
    text-align: center;
    padding: 60px 20px;
    color: #666;
}

.placeholder-icon {
    margin-bottom: 20px;
    color: #ccc;
}

.feature-list {
    margin-top: 30px;
    text-align: left;
    max-width: 300px;
    margin-left: auto;
    margin-right: auto;
}

.feature-item {
    display: flex;
    align-items: center;
    margin-bottom: 12px;
    gap: 12px;
}

.feature-item i {
    color: #28a745;
}

/* 其他样式 */
.form-control {
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
}

.form-group {
    margin-bottom: 16px;
}

.form-group label {
    display: block;
    margin-bottom: 4px;
    font-weight: 500;
    color: #555;
}
</style>
`;

// RBAC专用样式
const rbacStyles = `
<style>
/* 角色管理样式 */
.role-stats {
    display: flex;
    gap: 20px;
    margin-bottom: 30px;
}

.stat-item {
    display: flex;
    align-items: center;
    background: white;
    padding: 15px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    flex: 1;
}

.stat-item i {
    font-size: 24px;
    margin-right: 12px;
    color: #3498db;
}

.stat-info .stat-number {
    display: block;
    font-size: 24px;
    font-weight: bold;
    color: #2c3e50;
}

.stat-info .stat-label {
    font-size: 12px;
    color: #666;
}

.role-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.role-level {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
}

.role-level.high {
    background-color: #fee2e2;
    color: #dc2626;
}

.role-level.medium {
    background-color: #fef3c7;
    color: #d97706;
}

.role-level.low {
    background-color: #dcfce7;
    color: #16a34a;
}

.role-description {
    color: #666;
    font-size: 14px;
    margin-bottom: 15px;
    line-height: 1.4;
}

.role-stats-mini {
    display: flex;
    gap: 15px;
    margin-bottom: 15px;
    font-size: 12px;
    color: #666;
}

.role-stats-mini span {
    display: flex;
    align-items: center;
    gap: 4px;
}

.permission-group {
    margin-bottom: 12px;
}

.group-title {
    display: block;
    font-size: 12px;
    font-weight: 600;
    color: #374151;
    margin-bottom: 6px;
}

.admin-role {
    border-left: 4px solid #dc2626;
}

.operator-role {
    border-left: 4px solid #d97706;
}

.viewer-role {
    border-left: 4px solid #16a34a;
}

/* 角色权限矩阵 */
.role-permission-matrix {
    margin-top: 40px;
}

.matrix-table {
    overflow-x: auto;
}

.permission-matrix {
    width: 100%;
    border-collapse: collapse;
    background: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.permission-matrix th,
.permission-matrix td {
    padding: 12px;
    text-align: center;
    border-bottom: 1px solid #e5e7eb;
}

.permission-matrix th {
    background-color: #f9fafb;
    font-weight: 600;
    color: #374151;
}

.permission-matrix td:first-child {
    text-align: left;
    font-weight: 500;
}

.granted {
    color: #16a34a;
}

.denied {
    color: #dc2626;
}

.read-only {
    color: #2563eb;
}

.partial {
    color: #d97706;
}

/* 用户管理样式 */
.user-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background: white;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    display: flex;
    align-items: center;
}

.stat-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
    font-size: 20px;
    color: white;
}

.stat-card:nth-child(1) .stat-icon {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stat-card:nth-child(2) .stat-icon {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.stat-card:nth-child(3) .stat-icon {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.stat-card:nth-child(4) .stat-icon {
    background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.stat-content h4 {
    margin-bottom: 5px;
    color: #374151;
    font-size: 14px;
}

.stat-content .stat-number {
    font-size: 24px;
    font-weight: bold;
    color: #1f2937;
    display: block;
    margin-bottom: 2px;
}

.stat-content small {
    color: #6b7280;
    font-size: 12px;
}

/* 搜索和筛选 */
.search-filters {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    gap: 20px;
}

.search-box {
    position: relative;
    flex: 1;
    max-width: 300px;
}

.search-box i {
    position: absolute;
    left: 12px;
    top: 50%;
    transform: translateY(-50%);
    color: #6b7280;
}

.search-input {
    width: 100%;
    padding: 10px 12px 10px 40px;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    font-size: 14px;
}

.filter-group {
    display: flex;
    gap: 10px;
}

.filter-select {
    padding: 8px 12px;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    font-size: 14px;
    background: white;
}

/* 用户信息样式 */
.user-info {
    display: flex;
    align-items: center;
    gap: 10px;
}

.user-avatar {
    font-size: 24px;
    color: #6b7280;
}

.user-details strong {
    display: block;
    color: #1f2937;
}

.user-details small {
    color: #6b7280;
    font-size: 12px;
}

.user-roles {
    display: flex;
    flex-wrap: wrap;
    gap: 5px;
}

.role-badge {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
}

.role-badge.admin {
    background-color: #fee2e2;
    color: #dc2626;
}

.role-badge.operator {
    background-color: #fef3c7;
    color: #d97706;
}

.role-badge.viewer {
    background-color: #dcfce7;
    color: #16a34a;
}

.permission-summary {
    text-align: center;
}

.permission-count {
    display: block;
    font-size: 12px;
    color: #6b7280;
    margin-bottom: 5px;
}

.permission-preview {
    display: flex;
    flex-wrap: wrap;
    gap: 3px;
    justify-content: center;
}

.perm-tag {
    padding: 2px 6px;
    background-color: #e5e7eb;
    color: #374151;
    border-radius: 3px;
    font-size: 10px;
}

.action-buttons {
    display: flex;
    gap: 5px;
}

.action-buttons .btn {
    padding: 6px 8px;
}

/* 分页样式 */
.pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 15px;
    margin-top: 20px;
    padding: 20px 0;
}

.page-info {
    color: #6b7280;
    font-size: 14px;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .search-filters {
        flex-direction: column;
        align-items: stretch;
    }

    .search-box {
        max-width: none;
    }

    .user-stats {
        grid-template-columns: 1fr;
    }

    .role-stats {
        flex-direction: column;
    }

    .action-buttons {
        flex-direction: column;
    }
}
</style>
`;

// 系统配置和API管理样式
const systemStyles = `
<style>
/* 系统配置管理样式 */
.config-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.param-info strong {
    display: block;
    color: #1f2937;
    font-size: 14px;
}

.param-key {
    color: #6b7280;
    font-size: 11px;
    background: #f3f4f6;
    padding: 2px 6px;
    border-radius: 3px;
    margin-top: 2px;
    display: inline-block;
}

.param-value {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.value-input {
    padding: 6px 8px;
    border: 1px solid #d1d5db;
    border-radius: 4px;
    font-size: 13px;
    width: 120px;
}

.value-input.modified {
    border-color: #f59e0b;
    background-color: #fffbeb;
}

.value-type {
    font-size: 10px;
    color: #6b7280;
    background: #f9fafb;
    padding: 2px 4px;
    border-radius: 2px;
    align-self: flex-start;
}

.category-tag {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
}

.category-tag.database {
    background-color: #dbeafe;
    color: #1e40af;
}

.category-tag.cache {
    background-color: #fef3c7;
    color: #d97706;
}

.category-tag.security {
    background-color: #fee2e2;
    color: #dc2626;
}

.category-tag.business {
    background-color: #dcfce7;
    color: #16a34a;
}

.status-badge.pending {
    background-color: #fef3c7;
    color: #d97706;
}

/* 字典管理样式 */
.dict-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.dict-type-info strong {
    display: block;
    color: #1f2937;
    font-size: 14px;
}

.dict-code {
    color: #6b7280;
    font-size: 11px;
    background: #f3f4f6;
    padding: 2px 6px;
    border-radius: 3px;
    margin-top: 2px;
    display: inline-block;
}

.item-count {
    text-align: center;
}

.count-number {
    display: block;
    font-size: 18px;
    font-weight: bold;
    color: #1f2937;
}

.count-number + small {
    color: #6b7280;
    font-size: 11px;
}

.type-tag {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
}

.type-tag.system {
    background-color: #dbeafe;
    color: #1e40af;
}

.type-tag.business {
    background-color: #dcfce7;
    color: #16a34a;
}

.type-tag.config {
    background-color: #fef3c7;
    color: #d97706;
}

.dict-items-section {
    margin-top: 40px;
    padding-top: 20px;
    border-top: 1px solid #e5e7eb;
}

.dict-items-section h3 {
    color: #374151;
    margin-bottom: 15px;
}

.dict-items-table code {
    background: #f3f4f6;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 12px;
    color: #374151;
}

/* API管理样式 */
.api-info strong {
    display: block;
    color: #1f2937;
    font-size: 14px;
}

.api-desc {
    color: #6b7280;
    font-size: 12px;
    margin-top: 2px;
}

.method-badge {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
}

.method-badge.get {
    background-color: #dcfce7;
    color: #16a34a;
}

.method-badge.post {
    background-color: #dbeafe;
    color: #1e40af;
}

.method-badge.put {
    background-color: #fef3c7;
    color: #d97706;
}

.method-badge.delete {
    background-color: #fee2e2;
    color: #dc2626;
}

.module-tag {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
}

.module-tag.user {
    background-color: #e0e7ff;
    color: #3730a3;
}

.module-tag.permission {
    background-color: #fce7f3;
    color: #be185d;
}

.module-tag.business {
    background-color: #ecfdf5;
    color: #047857;
}

.response-time {
    text-align: center;
}

.time-value {
    display: block;
    font-weight: 600;
    color: #1f2937;
}

.time-status {
    font-size: 11px;
    margin-top: 2px;
}

.time-status.good {
    color: #16a34a;
}

.time-status.normal {
    color: #d97706;
}

.time-status.slow {
    color: #dc2626;
}

.call-count {
    text-align: center;
}

.count-number {
    display: block;
    font-weight: 600;
    color: #1f2937;
}

.call-count small {
    color: #6b7280;
    font-size: 11px;
}

/* 头部操作按钮组 */
.header-actions {
    display: flex;
    gap: 10px;
}

.header-actions .btn {
    display: flex;
    align-items: center;
    gap: 6px;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .config-stats,
    .dict-stats {
        grid-template-columns: 1fr;
    }

    .header-actions {
        flex-direction: column;
    }

    .search-filters {
        flex-direction: column;
        gap: 10px;
    }

    .filter-group {
        flex-direction: column;
        gap: 8px;
    }
}
</style>
`;

// 业务逻辑管理样式
const businessStyles = `
<style>
/* 业务逻辑管理样式 */
.business-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.rule-info strong {
    display: block;
    color: #1f2937;
    font-size: 14px;
}

.rule-desc {
    color: #6b7280;
    font-size: 12px;
    margin-top: 2px;
    line-height: 1.4;
}

.rule-type {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
}

.rule-type.validation {
    background-color: #dbeafe;
    color: #1e40af;
}

.rule-type.calculation {
    background-color: #dcfce7;
    color: #16a34a;
}

.rule-type.workflow {
    background-color: #fef3c7;
    color: #d97706;
}

.rule-type.notification {
    background-color: #fce7f3;
    color: #be185d;
}

.trigger-condition {
    text-align: center;
}

.condition-text {
    display: block;
    font-weight: 500;
    color: #1f2937;
    font-size: 13px;
}

.condition-detail {
    color: #6b7280;
    font-size: 11px;
    background: #f3f4f6;
    padding: 2px 6px;
    border-radius: 3px;
    margin-top: 2px;
    display: inline-block;
}

.execution-count {
    text-align: center;
}

.execution-count .count-number {
    display: block;
    font-weight: 600;
    color: #1f2937;
}

.execution-count small {
    color: #6b7280;
    font-size: 11px;
}

.success-rate {
    text-align: center;
}

.rate-value {
    display: block;
    font-weight: 600;
    color: #1f2937;
}

.rate-status {
    font-size: 11px;
    margin-top: 2px;
}

.rate-status.excellent {
    color: #16a34a;
}

.rate-status.good {
    color: #059669;
}

.rate-status.normal {
    color: #d97706;
}

.rate-status.poor {
    color: #dc2626;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .business-stats {
        grid-template-columns: 1fr;
    }
}
</style>
`;

// 首页和外部系统样式
const dashboardStyles = `
<style>
/* 首页仪表板样式 */
.dashboard-overview {
    margin-bottom: 30px;
}

.overview-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.overview-card {
    background: white;
    border-radius: 12px;
    padding: 24px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    display: flex;
    align-items: center;
    gap: 16px;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.overview-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0,0,0,0.15);
}

.card-icon {
    width: 60px;
    height: 60px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    color: white;
}

.card-icon.users {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.card-icon.services {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.card-icon.api {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.card-icon.alerts {
    background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.card-content h3 {
    margin: 0 0 8px 0;
    color: #374151;
    font-size: 14px;
    font-weight: 500;
}

.card-number {
    display: block;
    font-size: 32px;
    font-weight: 700;
    color: #1f2937;
    margin-bottom: 4px;
}

.card-trend {
    font-size: 12px;
    font-weight: 500;
}

.card-trend.up {
    color: #10b981;
}

.card-trend.stable {
    color: #6b7280;
}

.card-trend.warning {
    color: #f59e0b;
}

/* 快速操作面板 */
.quick-actions {
    margin-bottom: 30px;
}

.quick-actions h3 {
    color: #374151;
    margin-bottom: 16px;
    font-size: 18px;
}

.action-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 16px;
}

.action-item {
    background: white;
    border-radius: 8px;
    padding: 20px;
    text-align: center;
    cursor: pointer;
    transition: all 0.2s ease;
    border: 1px solid #e5e7eb;
}

.action-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    border-color: #3b82f6;
}

.action-item i {
    font-size: 24px;
    color: #3b82f6;
    margin-bottom: 8px;
    display: block;
}

.action-item span {
    color: #374151;
    font-size: 14px;
    font-weight: 500;
}

/* 系统状态监控 */
.system-status {
    margin-bottom: 30px;
}

.system-status h3 {
    color: #374151;
    margin-bottom: 16px;
    font-size: 18px;
}

.status-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
}

.status-item {
    background: white;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.status-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
}

.status-header h4 {
    margin: 0;
    color: #374151;
    font-size: 16px;
}

.status-indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
}

.status-indicator.online {
    background-color: #10b981;
}

.status-indicator.normal {
    background-color: #3b82f6;
}

.service-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #f3f4f6;
}

.service-item:last-child {
    border-bottom: none;
}

.service-name {
    font-weight: 500;
    color: #374151;
}

.service-status.running {
    color: #10b981;
    font-size: 12px;
}

.service-uptime {
    color: #6b7280;
    font-size: 12px;
}

.metric-item {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 12px;
}

.metric-label {
    min-width: 80px;
    font-size: 12px;
    color: #6b7280;
}

.metric-bar {
    flex: 1;
    height: 6px;
    background-color: #f3f4f6;
    border-radius: 3px;
    overflow: hidden;
}

.metric-fill {
    height: 100%;
    background: linear-gradient(90deg, #3b82f6, #1d4ed8);
    transition: width 0.3s ease;
}

.metric-value {
    min-width: 40px;
    text-align: right;
    font-size: 12px;
    font-weight: 500;
    color: #374151;
}

/* 最近活动 */
.recent-activities {
    margin-bottom: 30px;
}

.recent-activities h3 {
    color: #374151;
    margin-bottom: 16px;
    font-size: 18px;
}

.activity-list {
    background: white;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.activity-item {
    display: flex;
    align-items: flex-start;
    gap: 12px;
    padding: 12px 0;
    border-bottom: 1px solid #f3f4f6;
}

.activity-item:last-child {
    border-bottom: none;
}

.activity-icon {
    width: 32px;
    height: 32px;
    border-radius: 8px;
    background: #f3f4f6;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #6b7280;
    font-size: 14px;
}

.activity-content {
    flex: 1;
}

.activity-text {
    display: block;
    color: #374151;
    font-size: 14px;
    margin-bottom: 4px;
}

.activity-time {
    color: #6b7280;
    font-size: 12px;
}

/* 外部系统管理样式 */
.external-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.system-info strong {
    display: block;
    color: #1f2937;
    font-size: 14px;
}

.system-desc {
    color: #6b7280;
    font-size: 12px;
    margin-top: 2px;
    line-height: 1.4;
}

.system-type {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
}

.system-type.business {
    background-color: #dbeafe;
    color: #1e40af;
}

.system-type.database {
    background-color: #dcfce7;
    color: #16a34a;
}

.system-type.monitoring {
    background-color: #fef3c7;
    color: #d97706;
}

.system-type.api {
    background-color: #fce7f3;
    color: #be185d;
}

.connection-info {
    text-align: center;
}

.connection-url {
    display: block;
    font-weight: 500;
    color: #1f2937;
    font-size: 13px;
}

.connection-protocol {
    color: #6b7280;
    font-size: 11px;
    background: #f3f4f6;
    padding: 2px 6px;
    border-radius: 3px;
    margin-top: 2px;
    display: inline-block;
}

.sync-time {
    text-align: center;
}

.time-value {
    display: block;
    font-weight: 500;
    color: #1f2937;
}

.sync-status {
    font-size: 11px;
    margin-top: 2px;
}

.sync-status.success {
    color: #16a34a;
}

.sync-status.failed {
    color: #dc2626;
}

.data-volume {
    text-align: center;
}

.volume-number {
    display: block;
    font-weight: 600;
    color: #1f2937;
}

.data-volume small {
    color: #6b7280;
    font-size: 11px;
}

/* 系统架构图样式 */
.architecture-diagram {
    margin-bottom: 30px;
}

.architecture-diagram h3 {
    color: #374151;
    margin-bottom: 16px;
    font-size: 18px;
}

.arch-container {
    background: white;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.arch-layer {
    margin-bottom: 20px;
    padding: 16px;
    border-radius: 8px;
    border: 2px solid #e5e7eb;
}

.arch-layer.frontend {
    background: linear-gradient(135deg, #667eea20, #764ba220);
    border-color: #667eea;
}

.arch-layer.gateway {
    background: linear-gradient(135deg, #f093fb20, #f5576c20);
    border-color: #f093fb;
}

.arch-layer.services {
    background: linear-gradient(135deg, #4facfe20, #00f2fe20);
    border-color: #4facfe;
}

.arch-layer.data {
    background: linear-gradient(135deg, #43e97b20, #38f9d720);
    border-color: #43e97b;
}

.arch-layer h4 {
    margin: 0 0 12px 0;
    color: #374151;
    font-size: 14px;
    font-weight: 600;
}

.arch-components {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
}

.component {
    background: white;
    padding: 6px 12px;
    border-radius: 6px;
    font-size: 12px;
    font-weight: 500;
    color: #374151;
    border: 1px solid #d1d5db;
    box-shadow: 0 1px 2px rgba(0,0,0,0.05);
}

/* 响应式调整 */
@media (max-width: 768px) {
    .overview-cards,
    .external-stats {
        grid-template-columns: 1fr;
    }

    .action-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .status-grid {
        grid-template-columns: 1fr;
    }

    .arch-components {
        flex-direction: column;
    }

    .component {
        text-align: center;
    }
}
</style>
`;

// 任务管理样式
const taskStyles = `
<style>
/* 任务配置和监控样式 */
.task-stats,
.monitor-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.task-info strong {
    display: block;
    color: #1f2937;
    font-size: 14px;
}

.task-desc {
    color: #6b7280;
    font-size: 12px;
    margin-top: 2px;
    line-height: 1.4;
}

.task-type {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
}

.task-type.sync {
    background-color: #dbeafe;
    color: #1e40af;
}

.task-type.report {
    background-color: #dcfce7;
    color: #16a34a;
}

.task-type.cleanup {
    background-color: #fef3c7;
    color: #d97706;
}

.task-type.maintenance {
    background-color: #fce7f3;
    color: #be185d;
}

.schedule-info {
    text-align: center;
}

.schedule-text {
    display: block;
    font-weight: 500;
    color: #1f2937;
    font-size: 13px;
}

.schedule-cron {
    color: #6b7280;
    font-size: 11px;
    background: #f3f4f6;
    padding: 2px 6px;
    border-radius: 3px;
    margin-top: 2px;
    display: inline-block;
    font-family: monospace;
}

.next-execution {
    text-align: center;
}

.next-time {
    display: block;
    font-weight: 500;
    color: #1f2937;
}

.time-remaining {
    color: #6b7280;
    font-size: 11px;
}

.success-rate {
    text-align: center;
}

.rate-value {
    display: block;
    font-weight: 600;
    color: #16a34a;
    font-size: 14px;
}

.rate-detail {
    color: #6b7280;
    font-size: 11px;
}

/* 任务监控特有样式 */
.monitor-charts {
    margin-bottom: 30px;
}

.chart-container {
    background: white;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.chart-container h3 {
    margin: 0 0 16px 0;
    color: #374151;
    font-size: 16px;
}

.chart-placeholder {
    height: 200px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #f9fafb;
    border-radius: 8px;
}

.chart-mock {
    text-align: center;
}

.chart-bars {
    display: flex;
    align-items: end;
    gap: 8px;
    height: 120px;
    margin-bottom: 10px;
}

.bar {
    width: 20px;
    background: linear-gradient(to top, #3b82f6, #1d4ed8);
    border-radius: 2px 2px 0 0;
    min-height: 10px;
}

.chart-labels {
    display: flex;
    gap: 8px;
}

.chart-labels span {
    width: 20px;
    font-size: 11px;
    color: #6b7280;
}

.execution-list {
    margin-bottom: 30px;
}

.execution-list h3 {
    color: #374151;
    margin-bottom: 16px;
    font-size: 18px;
}

.time-info span {
    display: block;
    font-weight: 500;
    color: #1f2937;
}

.time-info small {
    color: #6b7280;
    font-size: 11px;
}

.progress-info {
    display: flex;
    align-items: center;
    gap: 8px;
}

.progress-bar {
    flex: 1;
    height: 6px;
    background-color: #f3f4f6;
    border-radius: 3px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #10b981, #059669);
    transition: width 0.3s ease;
}

.progress-text {
    font-size: 12px;
    font-weight: 500;
    color: #374151;
    min-width: 35px;
}

.estimate-info span {
    display: block;
    font-weight: 500;
    color: #1f2937;
}

.estimate-info small {
    color: #6b7280;
    font-size: 11px;
}

.resource-info {
    text-align: center;
}

.resource-info small {
    display: block;
    color: #6b7280;
    font-size: 11px;
    margin-bottom: 2px;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .task-stats,
    .monitor-stats {
        grid-template-columns: 1fr;
    }

    .chart-bars {
        gap: 4px;
    }

    .bar {
        width: 15px;
    }

    .chart-labels span {
        width: 15px;
        font-size: 10px;
    }
}
</style>
`;

// 新增模块样式
const additionalModulesStyles = `
<style>
/* API管理样式 */
.api-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

/* 任务日志样式 */
.log-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

/* 性能监控样式 */
.performance-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

/* 报警管理样式 */
.alert-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

/* 指标管理样式 */
.metrics-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

/* 系统日志样式 */
.log-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

/* 操作日志样式 */
.operation-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

/* 错误日志样式 */
.error-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.error-level {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
}

.error-level.critical {
    background-color: #fee2e2;
    color: #dc2626;
}

.error-level.normal {
    background-color: #fef3c7;
    color: #d97706;
}

.error-level.minor {
    background-color: #dcfce7;
    color: #16a34a;
}

.error-content {
    display: flex;
    flex-direction: column;
}

.error-content strong {
    color: #1f2937;
    margin-bottom: 2px;
}

.error-content small {
    color: #6b7280;
    font-size: 0.75rem;
    font-family: monospace;
}

.impact-info {
    display: flex;
    flex-direction: column;
}

.impact-level {
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 0.75rem;
    font-weight: 500;
    margin-bottom: 2px;
}

.impact-level.high {
    background-color: #fee2e2;
    color: #dc2626;
}

.impact-level.medium {
    background-color: #fef3c7;
    color: #d97706;
}

.impact-level.low {
    background-color: #dcfce7;
    color: #16a34a;
}

.impact-info small {
    color: #6b7280;
    font-size: 0.75rem;
}

.status-badge.processing {
    background-color: #dbeafe;
    color: #1e40af;
}

.status-badge.fixed {
    background-color: #dcfce7;
    color: #16a34a;
}

.status-badge.pending {
    background-color: #fef3c7;
    color: #d97706;
}

/* 接口文档样式 */
.docs-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.api-info {
    display: flex;
    flex-direction: column;
}

.api-info strong {
    color: #1f2937;
    margin-bottom: 2px;
}

.api-info small {
    color: #6b7280;
    font-size: 0.75rem;
}

.path-info code {
    background: #f3f4f6;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 12px;
    color: #374151;
    font-family: monospace;
}

.module-info {
    display: flex;
    flex-direction: column;
}

.module-info span {
    font-weight: 500;
    color: #1f2937;
}

.module-info small {
    color: #6b7280;
    font-size: 0.75rem;
}

.doc-status {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
}

.doc-status.complete {
    background-color: #dcfce7;
    color: #16a34a;
}

.doc-status.incomplete {
    background-color: #fef3c7;
    color: #d97706;
}

.version-info {
    display: flex;
    flex-direction: column;
}

.version-info span {
    font-weight: 500;
    color: #1f2937;
}

.version-info small {
    color: #6b7280;
    font-size: 0.75rem;
}

.update-time {
    display: flex;
    flex-direction: column;
}

.update-time span {
    font-weight: 500;
    color: #1f2937;
}

.update-time small {
    color: #6b7280;
    font-size: 0.75rem;
}

/* 数据同步样式 */
.sync-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.source-info,
.target-info {
    display: flex;
    flex-direction: column;
}

.source-name,
.target-name {
    font-weight: 500;
    color: #1f2937;
}

.source-info small,
.target-info small {
    color: #6b7280;
    font-size: 0.75rem;
    font-family: monospace;
}

.sync-type {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
}

.sync-type.realtime {
    background-color: #dcfce7;
    color: #16a34a;
}

.sync-type.scheduled {
    background-color: #dbeafe;
    color: #1e40af;
}

.sync-type.manual {
    background-color: #fef3c7;
    color: #d97706;
}

.progress-fill.error {
    background: linear-gradient(90deg, #ef4444, #dc2626);
}

.sync-time {
    display: flex;
    flex-direction: column;
}

.sync-time span {
    font-weight: 500;
    color: #1f2937;
}

.sync-time small {
    color: #6b7280;
    font-size: 0.75rem;
}

/* 消息队列样式 */
.queue-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.queue-info {
    display: flex;
    flex-direction: column;
}

.queue-info strong {
    color: #1f2937;
    margin-bottom: 2px;
}

.queue-info small {
    color: #6b7280;
    font-size: 0.75rem;
}

.queue-type {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
}

.queue-type.rocketmq {
    background-color: #fee2e2;
    color: #dc2626;
}

.queue-type.kafka {
    background-color: #dbeafe;
    color: #1e40af;
}

.queue-type.rabbitmq {
    background-color: #fef3c7;
    color: #d97706;
}

.message-stats {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.message-stats .pending {
    color: #d97706;
    font-size: 0.75rem;
}

.message-stats .processed {
    color: #16a34a;
    font-size: 0.75rem;
}

.consumer-info {
    display: flex;
    flex-direction: column;
}

.consumer-count {
    font-weight: 500;
    color: #1f2937;
}

.consumer-info small {
    color: #6b7280;
    font-size: 0.75rem;
}

.speed-info {
    display: flex;
    flex-direction: column;
}

.speed-value {
    font-weight: 500;
    color: #1f2937;
}

.speed-trend {
    font-size: 0.75rem;
    font-weight: 500;
}

.speed-trend.up {
    color: #16a34a;
}

.speed-trend.stable {
    color: #6b7280;
}

.speed-trend.down {
    color: #dc2626;
}

.status-badge.warning {
    background-color: #fef3c7;
    color: #d97706;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .api-stats,
    .log-stats,
    .performance-stats,
    .alert-stats,
    .metrics-stats,
    .operation-stats,
    .error-stats,
    .docs-stats,
    .sync-stats,
    .queue-stats {
        grid-template-columns: 1fr;
    }
}
</style>
`;

// 将额外样式添加到页面
document.head.insertAdjacentHTML('beforeend', additionalStyles);
document.head.insertAdjacentHTML('beforeend', rbacStyles);
document.head.insertAdjacentHTML('beforeend', systemStyles);
document.head.insertAdjacentHTML('beforeend', businessStyles);
document.head.insertAdjacentHTML('beforeend', dashboardStyles);
document.head.insertAdjacentHTML('beforeend', taskStyles);
document.head.insertAdjacentHTML('beforeend', additionalModulesStyles);